#!/bin/bash
# Script to revert dual AWS account changes and restore original configuration

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() { echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%H:%M:%S')] WARNING: $1${NC}"; }
error() { echo -e "${RED}[$(date +'%H:%M:%S')] ERROR: $1${NC}"; exit 1; }
info() { echo -e "${BLUE}[$(date +'%H:%M:%S')] INFO: $1${NC}"; }

# Configuration
APP_DIR="/opt/chainlit_rag"
BACKUP_DIR="/opt/chainlit_rag/backups/revert-$(date +%Y%m%d_%H%M%S)"

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root (use sudo)"
    fi
}

# Create backup before reverting
create_backup() {
    log "Creating backup before reverting changes..."
    mkdir -p "$BACKUP_DIR"
    
    # Backup current files
    for file in aws_utils.py ingest.py .env; do
        if [[ -f "$APP_DIR/$file" ]]; then
            cp "$APP_DIR/$file" "$BACKUP_DIR/"
            log "Backed up $file"
        fi
    done
    
    log "Backup created at: $BACKUP_DIR"
}

# Stop services
stop_services() {
    log "Stopping RAG services..."
    
    local services=("rag-api" "rag-frontend")
    
    for service in "${services[@]}"; do
        if systemctl is-active --quiet "${service}.service"; then
            systemctl stop "${service}.service"
            log "Stopped ${service}.service"
        fi
    done
}

# Revert aws_utils.py to original single account configuration
revert_aws_utils() {
    log "Reverting aws_utils.py to original configuration..."
    
    local aws_utils_file="$APP_DIR/aws_utils.py"
    
    if [[ ! -f "$aws_utils_file" ]]; then
        error "aws_utils.py not found at $aws_utils_file"
    fi
    
    # Create the original aws_utils.py configuration
    cat > "$aws_utils_file" << 'EOF'
# aws_utils.py - Unstructured.io integration (streamlined)
import boto3
import os
from typing import List, Optional
import tempfile
import re
import base64
from io import BytesIO
from PIL import Image

# Define function to convert elements to text format
def elements_to_text_format(elements):
    """Convert elements to a formatted text representation"""
    if not elements:
        return ""
    
    result = []
    for element in elements:
        if hasattr(element, "text"):
            result.append(element.text)
    
    return "\n\n".join(result)

def simple_clean_whitespace(text):
    """Simple function to clean extra whitespace"""
    if not text:
        return text
    # Replace multiple spaces with a single space
    text = re.sub(r'\s+', ' ', text)
    # Trim leading/trailing whitespace
    return text.strip()

class AWSClient:
    def __init__(self):
        self.region = os.getenv('AWS_REGION')
        self.access_key = os.getenv('AWS_ACCESS_KEY_ID')
        self.secret_key = os.getenv('AWS_SECRET_ACCESS_KEY')
        self.s3_client = boto3.client(
            's3',
            region_name=self.region,
            aws_access_key_id=self.access_key,
            aws_secret_access_key=self.secret_key
        )
        self._init_unstructured()

    def _init_unstructured(self):
        # No API key needed for local unstructured processing
        pass

    def test_connection(self) -> dict:
        bucket_name = os.getenv('S3_BUCKET_NAME')
        self.s3_client.head_bucket(Bucket=bucket_name)
        response = self.s3_client.list_objects_v2(Bucket=bucket_name, MaxKeys=1)
        has_objects = 'Contents' in response
        return {
            "status": "success", 
            "bucket": bucket_name,
            "accessible": True,
            "has_objects": has_objects,
            "parser_status": "unstructured_initialized"
        }

    def list_s3_files(self, bucket_name: str) -> List[str]:
        response = self.s3_client.list_objects_v2(Bucket=bucket_name)
        if 'Contents' not in response:
            return []
        return [obj['Key'] for obj in response['Contents']]

    def read_s3_file(self, bucket_name: str, file_key: str) -> Optional[dict]:
        try:
            response = self.s3_client.get_object(Bucket=bucket_name, Key=file_key)
            content_bytes = response['Body'].read()
        except Exception as e:
            print(f"Error reading S3 file {file_key}: {e}")
            return None
        
        head_response = self.s3_client.head_object(Bucket=bucket_name, Key=file_key)
        content_type = head_response.get('ContentType', 'unknown')
        file_extension = os.path.splitext(file_key)[1].lower()
        
        if file_extension == '.pdf' or 'pdf' in content_type.lower():
            parsed_content = self._parse_pdf_with_unstructured(content_bytes, file_key)
            return {"text": parsed_content, "images": []}
        elif file_extension == '.docx' or 'officedocument.wordprocessingml.document' in content_type.lower():
            return self._parse_docx_file(content_bytes, file_key)
        else:
            text_content = self._parse_text_file(content_bytes)
            return {"text": text_content, "images": []}

    def _parse_pdf_with_unstructured(self, content_bytes: bytes, file_key: str) -> str:
        from unstructured.partition.pdf import partition_pdf
        import os
        import base64
        from io import BytesIO
        from PIL import Image
        
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
            temp_file.write(content_bytes)
            temp_file_path = temp_file.name
        
        try:
            elements = partition_pdf(
                filename=temp_file_path,
                strategy="hi_res",
                extract_images_in_pdf=True,
                extract_image_block_types=["Image", "Table"],
                extract_image_block_to_payload=False,
                extract_image_block_output_dir=None,
                infer_table_structure=True,
                chunking_strategy="by_title",
                max_characters=1500,
                new_after_n_chars=1200,
                combine_text_under_n_chars=150
            )
            
            text_content = elements_to_text_format(elements)
            return simple_clean_whitespace(text_content)
            
        except Exception as e:
            print(f"Error parsing PDF {file_key} with unstructured: {e}")
            return ""
        finally:
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    def _parse_text_file(self, content_bytes: bytes) -> str:
        try:
            text_content = content_bytes.decode('utf-8')
            return simple_clean_whitespace(text_content)
        except UnicodeDecodeError:
            try:
                text_content = content_bytes.decode('latin-1')
                return simple_clean_whitespace(text_content)
            except Exception as e:
                print(f"Error decoding text file: {e}")
                return ""

    def _parse_docx_file(self, content_bytes: bytes, file_key: str) -> dict:
        from unstructured.partition.docx import partition_docx
        import os
        from io import BytesIO
        from PIL import Image
        
        with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
            temp_file.write(content_bytes)
            temp_file_path = temp_file.name
        
        try:
            elements = partition_docx(
                filename=temp_file_path,
                extract_images_in_docx=True,
                extract_image_block_types=["Image", "Table"],
                extract_image_block_to_payload=False,
                extract_image_block_output_dir=None,
                infer_table_structure=True,
                chunking_strategy="by_title",
                max_characters=1500,
                new_after_n_chars=1200,
                combine_text_under_n_chars=150
            )
            
            text_content = elements_to_text_format(elements)
            return {"text": simple_clean_whitespace(text_content), "images": []}
            
        except Exception as e:
            print(f"Error parsing DOCX {file_key} with unstructured: {e}")
            return {"text": "", "images": []}
        finally:
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
EOF
    
    # Set proper permissions
    chown raguser:raguser "$aws_utils_file"
    
    log "✅ aws_utils.py reverted to original single account configuration"
}

# Revert ingest.py to remove dual account comments
revert_ingest() {
    log "Reverting ingest.py to original configuration..."
    
    local ingest_file="$APP_DIR/ingest.py"
    
    if [[ ! -f "$ingest_file" ]]; then
        error "ingest.py not found at $ingest_file"
    fi
    
    # Remove the dual account comment that was added
    sed -i 's/# Use Bedrock credentials (your account) for embeddings//' "$ingest_file"
    
    log "✅ ingest.py reverted to original configuration"
}

# Clean up dual AWS account files
cleanup_dual_aws_files() {
    log "Cleaning up dual AWS account configuration files..."
    
    local files_to_remove=(
        "update-s3-config.sh"
        "validate-dual-aws-config.py"
        "test-s3-ingestion.sh"
        "update-env-dual-aws.sh"
        "generate-dual-env.sh"
        "DUAL-AWS-ACCOUNT-SETUP.md"
    )
    
    for file in "${files_to_remove[@]}"; do
        if [[ -f "$APP_DIR/$file" ]]; then
            rm -f "$APP_DIR/$file"
            log "Removed $file"
        elif [[ -f "$file" ]]; then
            rm -f "$file"
            log "Removed $file"
        fi
    done
    
    log "✅ Dual AWS account files cleaned up"
}

# Start services
start_services() {
    log "Starting RAG services..."
    
    # Start API service first
    systemctl start rag-api.service
    sleep 10
    
    # Check if API is ready
    local timeout=30
    while [[ $timeout -gt 0 ]]; do
        if curl -s -f http://localhost:8080/health >/dev/null 2>&1; then
            log "✓ RAG API is ready"
            break
        fi
        sleep 2
        ((timeout-=2))
    done
    
    # Start frontend service
    systemctl start rag-frontend.service
    sleep 5
    
    log "✓ Services started"
}

# Test original configuration
test_original_config() {
    log "Testing original single account configuration..."
    
    # Test API health
    if curl -s -f http://localhost:8080/health >/dev/null 2>&1; then
        log "✓ API health check passed"
    else
        warn "⚠ API health check failed"
    fi
    
    # Test ingestion
    local response=$(curl -s -X POST http://localhost:8080/ingest)
    if echo "$response" | grep -q '"status":"success"'; then
        log "✓ Ingestion test passed"
    else
        warn "⚠ Ingestion test may have issues"
        info "Response: $response"
    fi
}

# Show current configuration
show_current_config() {
    log "📋 Current Configuration (Reverted):"
    echo ""
    echo -e "${BLUE}=== Single AWS Account Configuration ===${NC}"
    echo "AWS_ACCESS_KEY_ID=AKIAU5LH6BIXI2EVAROB"
    echo "AWS_REGION=ap-south-1"
    echo "S3_BUCKET_NAME=knowlegde"
    echo "BEDROCK_EMBEDDING_MODEL_ID=amazon.titan-embed-text-v2:0"
    echo "BEDROCK_LLM_MODEL_ID=amazon.nova-micro-v1:0"
    echo ""
    info "✓ All services use the same AWS account credentials"
    info "✓ S3 and Bedrock operations use AWS_ACCESS_KEY_ID"
    echo ""
}

# Main execution
main() {
    log "🔄 Starting revert of dual AWS account changes"
    echo ""
    
    check_root
    create_backup
    stop_services
    revert_aws_utils
    revert_ingest
    cleanup_dual_aws_files
    start_services
    test_original_config
    show_current_config
    
    echo ""
    log "🎉 Successfully reverted to original single AWS account configuration!"
    echo ""
    info "Summary of changes reverted:"
    info "✓ aws_utils.py restored to use single AWS account"
    info "✓ ingest.py comments cleaned up"
    info "✓ Dual AWS account files removed"
    info "✓ Services restarted with original configuration"
    echo ""
    warn "Backup of current state created at: $BACKUP_DIR"
    echo ""
}

# Run main function
main "$@"
