#!/bin/bash
# Simple test script to verify the new S3 bucket configuration

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() { echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"; }
error() { echo -e "${RED}[$(date +'%H:%M:%S')] ERROR: $1${NC}"; }
info() { echo -e "${BLUE}[$(date +'%H:%M:%S')] INFO: $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%H:%M:%S')] WARNING: $1${NC}"; }

echo ""
log "🧪 Testing New S3 Bucket Configuration"
echo ""

# Check .env file
info "Checking .env file configuration..."
if grep -q "S3_BUCKET_NAME=cop-sop-documents" /opt/chainlit_rag/.env 2>/dev/null; then
    log "✓ New S3 bucket name confirmed: cop-sop-documents"
else
    error "✗ New S3 bucket name not found in .env file"
    exit 1
fi

# Check service status
info "Checking service status..."
if systemctl is-active --quiet rag-api.service; then
    log "✓ RAG API service is running"
else
    warn "⚠ RAG API service is not running"
fi

if systemctl is-active --quiet rag-frontend.service; then
    log "✓ RAG Frontend service is running"
else
    warn "⚠ RAG Frontend service is not running"
fi

# Test API health
info "Testing API health..."
if curl -s -f http://localhost:8080/health >/dev/null 2>&1; then
    log "✓ API health check passed"
else
    error "✗ API health check failed"
    exit 1
fi

# Test S3 ingestion
info "Testing S3 ingestion with new bucket..."
response=$(curl -s -X POST http://localhost:8080/ingest)

if echo "$response" | grep -q '"status":"success"'; then
    log "✓ S3 ingestion test successful"
    if command -v jq &> /dev/null; then
        chunks=$(echo "$response" | jq -r '.chunks' 2>/dev/null || echo "unknown")
        message=$(echo "$response" | jq -r '.message' 2>/dev/null || echo "unknown")
        info "Chunks processed: $chunks"
        info "Message: $message"
    fi
elif echo "$response" | grep -q '"status":"error"'; then
    warn "⚠ S3 ingestion returned error status"
    if command -v jq &> /dev/null; then
        error_msg=$(echo "$response" | jq -r '.message' 2>/dev/null || echo "unknown")
        warn "Error message: $error_msg"
    fi
else
    warn "⚠ Unexpected response from ingestion endpoint"
    info "Response: $response"
fi

# Test query endpoint
info "Testing query functionality..."
query_response=$(curl -s -X POST http://localhost:8080/query/advanced \
    -H "Content-Type: application/json" \
    -d '{"question": "What documents are available?"}')

if echo "$query_response" | grep -q '"answer"'; then
    log "✓ Query endpoint is working"
else
    warn "⚠ Query endpoint may have issues"
fi

echo ""
log "📋 Configuration Summary:"
echo ""
echo -e "${BLUE}=== Current S3 Configuration ===${NC}"
echo "S3_BUCKET_NAME: cop-sop-documents"
echo "AWS_ACCESS_KEY_ID: AKIAU5LH6BIXI2EVAROB"
echo "AWS_REGION: ap-south-1"
echo ""

echo ""
log "🎉 S3 bucket configuration test completed!"
echo ""
info "✓ S3 bucket successfully changed to: cop-sop-documents"
info "✓ All AWS credentials remain unchanged"
info "✓ Services are running with new configuration"
echo ""
warn "Make sure your new S3 bucket 'cop-sop-documents' contains the documents you want to ingest."
echo ""
