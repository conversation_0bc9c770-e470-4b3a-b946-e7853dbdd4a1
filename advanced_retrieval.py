"""
Advanced retrieval strategies for the RAG_Quadrant AWS cloud documentation system.

This module implements hybrid retrieval combining semantic search with keyword matching,
Maximal Marginal Relevance (MMR) for result diversity, and AWS-specific optimizations.
"""

from typing import List, Dict, Any, Optional, Union
from langchain.schema import Document
from langchain_community.retrievers import BM25Retriever
from langchain.retrievers.multi_query import MultiQueryRetriever
from langchain.retrievers.ensemble import EnsembleRetriever
from langchain.retrievers.contextual_compression import ContextualCompressionRetriever
from langchain.retrievers.document_compressors import DocumentCompressorPipeline
from langchain.retrievers.document_compressors.embeddings_filter import EmbeddingsFilter
from langchain.retrievers.self_query.base import SelfQueryRetriever
from langchain.cache import InMemoryCache
from langchain.globals import set_llm_cache
from langchain.vectorstores.base import VectorStore
from langchain_aws import ChatBedrock
import logging
import re
import os

logger = logging.getLogger(__name__)

# Set up Lang<PERSON>hain caching
set_llm_cache(InMemoryCache())


# Using <PERSON><PERSON><PERSON><PERSON>'s built-in EnsembleRetriever instead of custom implementation


class AWSBM25Retriever:
    """
    Enhanced BM25 retriever optimized for AWS terminology and cloud documentation.
    """

    def __init__(self, vectorstore: VectorStore, k: int = 4, preprocess_func: Optional[callable] = None):
        self.vectorstore = vectorstore
        self.k = k
        self.preprocess_func = preprocess_func or self._aws_preprocess

        # Get documents from vector store
        try:
            # Avoid sending empty string to embedding model (Bedrock will error)
            # Instead, fetch all docs with a non-empty dummy string unlikely to match anything
            dummy_query = "aws"  # Use a generic term to fetch a large corpus
            all_docs = vectorstore.similarity_search(dummy_query, k=1000)  # Get many docs for BM25 corpus
            if not all_docs:
                # Fallback: create empty retriever
                self.docs = []
                self.retriever = None
                return
        except Exception as e:
            logger.warning(f"Failed to initialize BM25Retriever: {e}")
            # Fallback: create empty retriever
            self.docs = []
            self.retriever = None
            return

        # Preprocess documents for BM25
        processed_docs = []
        for doc in all_docs:
            processed_content = self.preprocess_func(doc.page_content)
            processed_doc = Document(
                page_content=processed_content,
                metadata=doc.metadata.copy()
            )
            processed_docs.append(processed_doc)
        
        # Initialize LangChain's BM25Retriever
        self.retriever = BM25Retriever.from_documents(
            processed_docs,
            k=k
        )
        self.docs = all_docs
    
    def _aws_preprocess(self, text: str) -> str:
        """
        Preprocess text for AWS cloud documentation, preserving important terminology.
        """
        # Convert to lowercase but preserve AWS service names and technical terms
        text = text.lower()

        # Preserve common AWS patterns
        aws_patterns = [
            r'\bec2-\w+\b',      # EC2 instance types (e.g., ec2-micro)
            r'\bt\d+\.\w+\b',    # Instance types (e.g., t3.micro)
            r'\bm\d+\.\w+\b',    # Instance types (e.g., m5.large)
            r'\bc\d+\.\w+\b',    # Instance types (e.g., c5.xlarge)
            r'\br\d+\.\w+\b',    # Instance types (e.g., r5.large)
            r'\baws::\w+::\w+\b', # AWS resource types (e.g., aws::ec2::instance)
            r'\barn:aws:\w+:\w+:\d+:\w+\b', # ARNs
        ]

        # Extract and preserve these patterns
        preserved_terms = []
        for pattern in aws_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            preserved_terms.extend(matches)

        # Add preserved terms back to ensure they're not lost in processing
        text += " " + " ".join(preserved_terms)

        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text).strip()

        return text
    
    def get_relevant_documents(self, query: str) -> List[Document]:
        """Get relevant documents using BM25 scoring."""
        if not self.retriever:
            return []
        
        # Preprocess query
        processed_query = self.preprocess_func(query)
        
        # Get documents using LangChain's BM25Retriever
        results = self.retriever.get_relevant_documents(processed_query)
        
        # Map back to original documents and add scores
        original_docs = []
        for i, doc in enumerate(results):
            # Find the original document
            for orig_doc in self.docs:
                if orig_doc.metadata.get('id') == doc.metadata.get('id'):
                    # Create a copy with BM25 score
                    doc_copy = Document(
                        page_content=orig_doc.page_content,
                        metadata=orig_doc.metadata.copy()
                    )
                    # Add score based on position (higher rank = higher score)
                    doc_copy.metadata['bm25_score'] = 1.0 - (i / len(results)) if results else 0
                    original_docs.append(doc_copy)
                    break
        
        return original_docs


class HybridAWSRetriever:
    """
    Hybrid retriever combining semantic search and BM25 for AWS cloud documentation.
    This class is kept for backward compatibility but uses the new LangChain components.
    """
    
    def __init__(self, vectorstore: VectorStore, weights: List[float] = [0.7, 0.3], k: int = 8):
        """
        Initialize hybrid retriever.
        
        Args:
            vectorstore: LangChain vector store
            weights: Weights for [semantic, bm25] retrievers
            k: Number of documents to retrieve
        """
        self.vectorstore = vectorstore
        self.weights = weights
        self.k = k
        
        # Initialize retrievers
        self.semantic_retriever = vectorstore.as_retriever(
            search_type="similarity",
            search_kwargs={"k": k}
        )
        
        self.bm25_retriever = AWSBM25Retriever(
            vectorstore=vectorstore,
            k=k
        )
        
        # Create ensemble retriever using LangChain's implementation
        try:
            self.ensemble_retriever = EnsembleRetriever(
                retrievers=[self.semantic_retriever, self.bm25_retriever],
                weights=weights
            )
        except Exception as e:
            logger.warning(f"Failed to create ensemble retriever: {e}")
            self.ensemble_retriever = self.semantic_retriever
    
    def get_relevant_documents(self, query: str, use_mmr: bool = False, lambda_mult: float = 0.5) -> List[Document]:
        """
        Get relevant documents using hybrid retrieval.
        
        Args:
            query: Search query
            use_mmr: Whether to apply MMR for diversity
            lambda_mult: MMR lambda parameter (0=diversity, 1=relevance)
        """
        try:
            if use_mmr and hasattr(self.vectorstore, 'max_marginal_relevance_search'):
                # Use MMR for diverse results
                return self.vectorstore.max_marginal_relevance_search(
                    query=query,
                    k=self.k,
                    lambda_mult=lambda_mult
                )
            else:
                # Use ensemble retrieval
                return self.ensemble_retriever.get_relevant_documents(query)
        except Exception as e:
            logger.error(f"Hybrid retrieval failed: {e}")
            # Fallback to semantic search
            return self.semantic_retriever.get_relevant_documents(query)


class AWSQueryExpander:
    """
    Query expansion for AWS cloud documentation queries using domain knowledge.
    """

    def __init__(self, llm: ChatBedrock):
        self.llm = llm

        # AWS terminology mappings
        self.aws_synonyms = {
            "ec2": ["elastic compute cloud", "virtual machine", "instance"],
            "s3": ["simple storage service", "object storage", "bucket"],
            "rds": ["relational database service", "managed database"],
            "lambda": ["serverless", "function as a service", "faas"],
            "vpc": ["virtual private cloud", "network", "subnet"],
            "iam": ["identity and access management", "permissions", "roles"],
            "cloudformation": ["infrastructure as code", "template", "stack"],
            "cost": ["pricing", "billing", "expense", "budget"]
        }
        
        # LangChain prompt for query expansion
        self.multi_query_prompt = """You are an AI language model assistant that helps generate multiple search queries 
        based on a single input query specifically for AWS cloud documentation. 
        Your goal is to help the user find the most relevant information by generating 
        alternative ways of expressing the same information need.

        Given the following query: "{query}"

        Generate {num_queries} different versions of this query that capture the same intent but with variations 
        in wording, terminology (especially AWS terminology), or perspective.
        
        Focus on AWS-specific terminology and concepts. If the query mentions AWS services, include variations 
        with both abbreviated names (like S3, EC2) and full names (Simple Storage Service, Elastic Compute Cloud).
        
        Return only the list of queries with no additional text."""
    
    def expand_query(self, query: str, num_queries: int = 3) -> List[str]:
        """
        Expand query with AWS synonyms and related terms.

        Args:
            query: Original query
            num_queries: Number of expanded queries to generate

        Returns:
            List of expanded queries including the original
        """
        expanded_queries = [query]
        
        # First use rule-based expansions
        query_lower = query.lower()
        for term, synonyms in self.aws_synonyms.items():
            if term in query_lower:
                for synonym in synonyms:
                    expanded_query = query_lower.replace(term, synonym)
                    if expanded_query != query_lower:
                        expanded_queries.append(expanded_query)
        
        # Then use LLM-based expansion if available
        try:
            formatted_prompt = self.multi_query_prompt.format(query=query, num_queries=num_queries)
            response = self.llm.invoke(formatted_prompt)
            
            # Extract queries from response
            llm_queries = []
            for line in response.content.split('\n'):
                clean_line = line.strip()
                # Skip empty lines and numbered bullets
                if clean_line and not clean_line.startswith(('1.', '2.', '3.', '4.', '5.', '-')):
                    # Remove any quotes
                    clean_line = clean_line.strip('"\'')
                    if clean_line:
                        llm_queries.append(clean_line)
            
            # Add LLM-generated queries
            expanded_queries.extend(llm_queries)
            
        except Exception as e:
            logger.warning(f"LLM query expansion failed: {e}")
            # Fall back to technical expansions
            technical_expansions = self._generate_technical_expansions(query)
            expanded_queries.extend(technical_expansions)
        
        # Remove duplicates and limit
        unique_queries = list(dict.fromkeys(expanded_queries))
        return unique_queries[:num_queries+1]  # Original + num_queries
    
    def create_multi_query_retriever(self, retriever, query: str, num_queries: int = 3):
        """Create a MultiQueryRetriever using this expander"""
        try:
            # Use LangChain's MultiQueryRetriever
            multi_query_retriever = MultiQueryRetriever.from_llm(
                retriever=retriever,
                llm=self.llm,
                prompt_template=self.multi_query_prompt,
                parser_key="lines"
            )
            return multi_query_retriever
        except Exception as e:
            logger.warning(f"Failed to create MultiQueryRetriever: {e}")
            # Return original retriever as fallback
            return retriever
    
    def _generate_technical_expansions(self, query: str) -> List[str]:
        """Generate technical variations of the query."""
        expansions = []

        # Add service-specific terms
        if any(term in query.lower() for term in ["compare", "comparison", "versus"]):
            expansions.extend([
                query + " service",
                query + " feature",
                query + " option",
                query + " solution"
            ])

        # Add cost/performance terms
        if any(term in query.lower() for term in ["cost", "performance", "optimization"]):
            expansions.extend([
                query + " pricing",
                query + " efficiency",
                query + " best practice",
                query + " strategy"
            ])

        return expansions


class ConfigurableRetriever:
    """
    Configurable retriever with AWS-specific optimizations.
    """
    
    def __init__(self, vectorstore: VectorStore, llm: ChatBedrock, config: Optional[Dict[str, Any]] = None):
        self.vectorstore = vectorstore
        self.llm = llm
        
        # Default configuration
        default_config = {
            "similarity_threshold": 0.3,
            "max_results": 8,
            "use_hybrid": True,
            "hybrid_weights": [0.7, 0.3],  # [semantic, bm25]
            "use_mmr": False,
            "mmr_lambda": 0.5,
            "use_query_expansion": False,
            "use_compression": False,
            "aws_boost": True,  # Boost AWS terminology
            "filter_duplicates": True
        }
        
        self.config = {**default_config, **(config or {})}
        
        # Initialize base retrievers
        self.semantic_retriever = vectorstore.as_retriever(
            search_type="similarity",
            search_kwargs={"k": self.config["max_results"]}
        )
        
        self.mmr_retriever = vectorstore.as_retriever(
            search_type="mmr",
            search_kwargs={
                "k": self.config["max_results"],
                "lambda_mult": self.config["mmr_lambda"]
            }
        )
        
        # Initialize BM25 retriever
        self.bm25_retriever = AWSBM25Retriever(
            vectorstore=vectorstore,
            k=self.config["max_results"]
        )
        
        # Initialize hybrid retriever
        self.hybrid_retriever = self._create_hybrid_retriever()
        
        # Initialize query expander
        self.query_expander = AWSQueryExpander(llm)
        
        # Initialize contextual compression if embeddings available
        try:
            embeddings = vectorstore._embedding_function
            self.embeddings_filter = EmbeddingsFilter(
                embeddings=embeddings,
                similarity_threshold=self.config["similarity_threshold"]
            )
            
            # Create compression pipeline
            self.compressor = DocumentCompressorPipeline(
                transformers=[self.embeddings_filter]
            )
            
            # Create compressed retrievers
            self.compressed_retriever = ContextualCompressionRetriever(
                base_compressor=self.compressor,
                base_retriever=self.hybrid_retriever
            )
        except Exception as e:
            logger.warning(f"Failed to initialize compression: {e}")
            self.compressed_retriever = None
    
    def _create_hybrid_retriever(self):
        """Create hybrid retriever based on configuration"""
        try:
            return EnsembleRetriever(
                retrievers=[self.semantic_retriever, self.bm25_retriever],
                weights=self.config["hybrid_weights"]
            )
        except Exception as e:
            logger.warning(f"Failed to create ensemble retriever: {e}")
            return self.semantic_retriever
    
    def retrieve(self, query: str, **kwargs) -> List[Document]:
        """
        Retrieve documents with configurable strategies.
        
        Args:
            query: Search query
            **kwargs: Override configuration parameters
        """
        # Merge runtime config
        runtime_config = {**self.config, **kwargs}
        
        try:
            # Select base retriever based on configuration
            if runtime_config.get("use_mmr", False):
                base_retriever = self.mmr_retriever
            elif runtime_config.get("use_hybrid", True):
                base_retriever = self.hybrid_retriever
            else:
                base_retriever = self.semantic_retriever
            
            # Apply query expansion if configured
            if runtime_config.get("use_query_expansion", False):
                try:
                    # Create multi-query retriever
                    retriever = self.query_expander.create_multi_query_retriever(
                        base_retriever, 
                        query,
                        num_queries=3
                    )
                except Exception as e:
                    logger.warning(f"Multi-query retrieval failed: {e}")
                    retriever = base_retriever
            else:
                retriever = base_retriever
            
            # Apply contextual compression if configured
            if runtime_config.get("use_compression", False) and self.compressed_retriever:
                retriever = self.compressed_retriever
            
            # Retrieve documents
            documents = retriever.get_relevant_documents(query)
            
            # Apply AWS boost
            if runtime_config.get("aws_boost", True):
                documents = self._apply_aws_boost(documents, query)
            
            # Filter duplicates
            if runtime_config.get("filter_duplicates", True):
                documents = self._filter_duplicates(documents)
            
            return documents[:runtime_config.get("max_results", 8)]
            
        except Exception as e:
            logger.error(f"Configurable retrieval failed: {e}")
            # Fallback to simple search
            return self.vectorstore.similarity_search(query, k=runtime_config.get("max_results", 8))
    
    def _apply_aws_boost(self, documents: List[Document], query: str) -> List[Document]:
        """Apply boost to documents containing AWS terminology."""
        aws_terms = [
            "ec2", "lambda", "s3", "rds", "vpc", "cloudformation",
            "iam", "cloudwatch", "elb", "auto scaling", "cost", "performance",
            "architecture", "security", "networking", "storage", "compute",
            "instance", "bucket", "database", "serverless", "container"
        ]

        query_lower = query.lower()
        query_has_aws = any(term in query_lower for term in aws_terms)

        if not query_has_aws:
            return documents

        # Boost documents with AWS content
        for doc in documents:
            content_lower = doc.page_content.lower()
            aws_count = sum(1 for term in aws_terms if term in content_lower)

            if aws_count > 0:
                # Apply boost to score
                current_score = doc.metadata.get("score", 0.5)
                boost_factor = 1.0 + (aws_count * 0.1)  # 10% boost per AWS term
                doc.metadata["score"] = min(current_score * boost_factor, 1.0)
                doc.metadata["aws_boost"] = boost_factor

        # Re-sort by boosted scores
        documents.sort(key=lambda x: x.metadata.get("score", 0), reverse=True)

        return documents
    
    def _filter_duplicates(self, documents: List[Document]) -> List[Document]:
        """Filter out duplicate documents based on content similarity."""
        if len(documents) <= 1:
            return documents
        
        filtered = []
        seen_content = set()
        
        for doc in documents:
            # Create a simple hash of the content
            content_hash = hash(doc.page_content[:200])  # Use first 200 chars
            
            if content_hash not in seen_content:
                seen_content.add(content_hash)
                filtered.append(doc)
        
        return filtered
