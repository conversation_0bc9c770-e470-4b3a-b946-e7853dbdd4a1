#!/bin/bash
# Simple script to generate updated .env file content for dual AWS accounts
# Based on your current configuration

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${GREEN}🔧 Dual AWS Account .env File Generator${NC}"
echo ""

# Get new S3 configuration
echo -e "${BLUE}Enter your new S3 configuration:${NC}"
read -p "New S3 bucket name: " NEW_S3_BUCKET
read -p "S3 AWS Access Key ID: " NEW_S3_ACCESS_KEY
read -s -p "S3 AWS Secret Access Key: " NEW_S3_SECRET_KEY
echo ""
read -p "S3 AWS Region (default: ap-south-1): " NEW_S3_REGION
NEW_S3_REGION=${NEW_S3_REGION:-ap-south-1}

echo ""
echo -e "${GREEN}📋 Generated .env file content:${NC}"
echo -e "${YELLOW}================================${NC}"

cat << EOF
# AWS Configuration for Bedrock (Your Account)
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=AFkXvxcW0qvedmm5CrwQae3zW0j9V4wrZ0GT0Lqj
AWS_REGION=ap-south-1

# S3 Configuration (Different Account)
S3_BUCKET_NAME=${NEW_S3_BUCKET}
S3_AWS_ACCESS_KEY_ID=${NEW_S3_ACCESS_KEY}
S3_AWS_SECRET_ACCESS_KEY=${NEW_S3_SECRET_KEY}
S3_AWS_REGION=${NEW_S3_REGION}

# Vector Store Configuration
QDRANT_PATH=./vector_store

# Bedrock Configuration (Your Account)
BEDROCK_EMBEDDING_MODEL_ID=amazon.titan-embed-text-v2:0
BEDROCK_LLM_MODEL_ID=amazon.nova-micro-v1:0
BEDROCK_LLM_PROFILE_ARN=arn:aws:bedrock:ap-south-1:************:inference-profile/apac.amazon.nova-micro-v1:0

# Application Configuration
RAG_API_HOST=0.0.0.0
RAG_API_PORT=8080
API_BASE_URL=http://localhost:8080
CHAINLIT_HOST=0.0.0.0
CHAINLIT_PORT=80
EOF

echo -e "${YELLOW}================================${NC}"
echo ""
echo -e "${GREEN}📝 To apply this configuration:${NC}"
echo ""
echo "1. Copy the content above"
echo "2. On your EC2 instance, run:"
echo "   sudo nano /opt/chainlit_rag/.env"
echo "3. Replace the content with the generated configuration"
echo "4. Save and exit (Ctrl+X, Y, Enter)"
echo "5. Restart services:"
echo "   sudo systemctl restart rag-api.service rag-frontend.service"
echo ""
echo -e "${GREEN}🧪 To test the configuration:${NC}"
echo "   curl -X POST http://localhost:8080/ingest"
echo ""
