#!/bin/bash
# Script to update S3 bucket name from "knowlegde" to "cop-sop-documents"
# Keeps all other configuration unchanged

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() { echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%H:%M:%S')] WARNING: $1${NC}"; }
error() { echo -e "${RED}[$(date +'%H:%M:%S')] ERROR: $1${NC}"; exit 1; }
info() { echo -e "${BLUE}[$(date +'%H:%M:%S')] INFO: $1${NC}"; }

# Configuration
APP_DIR="/opt/chainlit_rag"
ENV_FILE="$APP_DIR/.env"
BACKUP_FILE="$APP_DIR/.env.backup.$(date +%Y%m%d_%H%M%S)"
OLD_BUCKET="knowlegde"
NEW_BUCKET="cop-sop-documents"

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root (use sudo)"
    fi
}

# Verify current configuration
verify_current_config() {
    log "Verifying current configuration..."
    
    if [[ ! -f "$ENV_FILE" ]]; then
        error "Environment file not found: $ENV_FILE"
    fi
    
    # Check current bucket name
    local current_bucket=$(grep "^S3_BUCKET_NAME=" "$ENV_FILE" | cut -d'=' -f2 || echo "")
    
    if [[ "$current_bucket" == "$OLD_BUCKET" ]]; then
        log "✓ Current S3 bucket confirmed: $OLD_BUCKET"
    else
        warn "Current S3 bucket is: $current_bucket (expected: $OLD_BUCKET)"
        read -p "Continue anyway? (y/N): " confirm
        if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
            error "Operation cancelled"
        fi
    fi
    
    # Verify AWS credentials are present
    if grep -q "^AWS_ACCESS_KEY_ID=AKIAU5LH6BIXI2EVAROB" "$ENV_FILE"; then
        log "✓ AWS credentials confirmed"
    else
        error "Expected AWS credentials not found in .env file"
    fi
}

# Create backup
create_backup() {
    log "Creating backup of current .env file..."
    cp "$ENV_FILE" "$BACKUP_FILE"
    log "Backup created: $BACKUP_FILE"
}

# Update S3 bucket name
update_bucket_name() {
    log "Updating S3 bucket name from '$OLD_BUCKET' to '$NEW_BUCKET'..."
    
    # Use sed to replace the bucket name
    sed -i "s/^S3_BUCKET_NAME=$OLD_BUCKET$/S3_BUCKET_NAME=$NEW_BUCKET/" "$ENV_FILE"
    
    # Verify the change
    local updated_bucket=$(grep "^S3_BUCKET_NAME=" "$ENV_FILE" | cut -d'=' -f2 || echo "")
    
    if [[ "$updated_bucket" == "$NEW_BUCKET" ]]; then
        log "✅ S3 bucket name successfully updated to: $NEW_BUCKET"
    else
        error "Failed to update S3 bucket name. Current value: $updated_bucket"
    fi
}

# Show updated configuration
show_updated_config() {
    log "📋 Updated .env Configuration:"
    echo ""
    echo -e "${BLUE}=== AWS Configuration (Unchanged) ===${NC}"
    grep "^AWS_ACCESS_KEY_ID=" "$ENV_FILE" || echo "AWS_ACCESS_KEY_ID not found"
    grep "^AWS_SECRET_ACCESS_KEY=" "$ENV_FILE" | sed 's/=.*/=***HIDDEN***/' || echo "AWS_SECRET_ACCESS_KEY not found"
    grep "^AWS_REGION=" "$ENV_FILE" || echo "AWS_REGION not found"
    echo ""
    echo -e "${BLUE}=== S3 Configuration (Updated) ===${NC}"
    grep "^S3_BUCKET_NAME=" "$ENV_FILE" || echo "S3_BUCKET_NAME not found"
    echo ""
    echo -e "${BLUE}=== Bedrock Configuration (Unchanged) ===${NC}"
    grep "^BEDROCK_EMBEDDING_MODEL_ID=" "$ENV_FILE" || echo "BEDROCK_EMBEDDING_MODEL_ID not found"
    grep "^BEDROCK_LLM_MODEL_ID=" "$ENV_FILE" || echo "BEDROCK_LLM_MODEL_ID not found"
    grep "^BEDROCK_LLM_PROFILE_ARN=" "$ENV_FILE" || echo "BEDROCK_LLM_PROFILE_ARN not found"
    echo ""
}

# Stop services
stop_services() {
    log "Stopping RAG services..."
    
    local services=("rag-api" "rag-frontend")
    
    for service in "${services[@]}"; do
        if systemctl is-active --quiet "${service}.service"; then
            systemctl stop "${service}.service"
            log "Stopped ${service}.service"
        else
            info "${service}.service was not running"
        fi
    done
}

# Start services
start_services() {
    log "Starting RAG services with new S3 bucket configuration..."
    
    # Start API service first
    log "Starting RAG API service..."
    systemctl start rag-api.service
    
    # Wait for API to be ready
    local api_ready=false
    local timeout=60
    
    info "Waiting for API to be ready..."
    while [[ $timeout -gt 0 ]] && [[ "$api_ready" == false ]]; do
        if curl -s -f http://localhost:8080/health >/dev/null 2>&1; then
            api_ready=true
            log "✓ RAG API is ready"
        else
            sleep 2
            ((timeout-=2))
        fi
    done
    
    if [[ "$api_ready" == false ]]; then
        error "RAG API failed to start within 60 seconds"
    fi
    
    # Start frontend service
    log "Starting RAG Frontend service..."
    systemctl start rag-frontend.service
    sleep 5
    
    # Verify both services are running
    for service in "rag-api" "rag-frontend"; do
        if systemctl is-active --quiet "${service}.service"; then
            log "✓ ${service}.service is running"
        else
            error "✗ ${service}.service failed to start"
        fi
    done
}

# Test new S3 bucket configuration
test_new_bucket() {
    log "Testing new S3 bucket configuration..."
    
    # Test API health
    if curl -s -f http://localhost:8080/health >/dev/null 2>&1; then
        log "✓ API health check passed"
    else
        error "✗ API health check failed"
    fi
    
    # Test S3 bucket access
    log "Testing S3 bucket access..."
    local response=$(curl -s -w "%{http_code}" -X POST http://localhost:8080/ingest -o /tmp/ingest_test.json)
    local http_code="${response: -3}"
    
    if [[ "$http_code" == "200" ]]; then
        log "✓ S3 ingestion endpoint responded successfully"
        
        # Parse response if jq is available
        if command -v jq &> /dev/null; then
            local status=$(jq -r '.status' /tmp/ingest_test.json 2>/dev/null || echo "unknown")
            local message=$(jq -r '.message' /tmp/ingest_test.json 2>/dev/null || echo "unknown")
            local chunks=$(jq -r '.chunks' /tmp/ingest_test.json 2>/dev/null || echo "unknown")
            
            info "Ingestion Status: $status"
            info "Message: $message"
            info "Chunks processed: $chunks"
            
            if [[ "$status" == "success" ]]; then
                log "✅ Document ingestion test successful with new bucket"
            else
                warn "⚠ Ingestion completed but with status: $status"
                info "This may be normal if the bucket is empty"
            fi
        else
            log "✓ Ingestion endpoint accessible (jq not available for detailed parsing)"
        fi
    else
        error "✗ S3 ingestion test failed (HTTP $http_code)"
        cat /tmp/ingest_test.json 2>/dev/null || echo "No response body"
    fi
    
    # Clean up
    rm -f /tmp/ingest_test.json
}

# Show next steps
show_next_steps() {
    echo ""
    log "🎯 Next Steps:"
    echo ""
    info "1. Verify the new S3 bucket 'cop-sop-documents' contains your documents"
    info "2. Test document ingestion:"
    echo "   curl -X POST http://localhost:8080/ingest"
    echo ""
    info "3. Test query functionality:"
    echo "   curl -X POST http://localhost:8080/query/advanced \\"
    echo "     -H 'Content-Type: application/json' \\"
    echo "     -d '{\"question\": \"What documents are available?\"}'"
    echo ""
    info "4. If you need to revert to the old bucket:"
    echo "   sudo cp $BACKUP_FILE $ENV_FILE"
    echo "   sudo systemctl restart rag-api.service rag-frontend.service"
    echo ""
}

# Main execution
main() {
    log "🚀 Starting S3 bucket update from '$OLD_BUCKET' to '$NEW_BUCKET'"
    echo ""
    
    check_root
    verify_current_config
    create_backup
    stop_services
    update_bucket_name
    show_updated_config
    start_services
    test_new_bucket
    show_next_steps
    
    echo ""
    log "🎉 S3 bucket update completed successfully!"
    echo ""
    info "Summary of changes:"
    info "✓ S3 bucket changed from '$OLD_BUCKET' to '$NEW_BUCKET'"
    info "✓ All AWS credentials remain unchanged"
    info "✓ All Bedrock configuration remains unchanged"
    info "✓ Services restarted with new configuration"
    info "✓ New bucket configuration tested"
    echo ""
    warn "Backup of original .env file: $BACKUP_FILE"
    echo ""
}

# Run main function
main "$@"
