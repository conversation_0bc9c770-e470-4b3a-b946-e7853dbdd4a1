#!/usr/bin/env python3
"""
Validation script for dual AWS account configuration
Tests both S3 access (different account) and Bedrock access (your account)
"""

import os
import sys
import boto3
from dotenv import load_dotenv
from langchain_aws import BedrockEmbeddings, ChatBedrock

def load_environment():
    """Load environment variables"""
    load_dotenv()
    
    # Check required environment variables
    required_vars = [
        'AWS_ACCESS_KEY_ID', 'AWS_SECRET_ACCESS_KEY', 'AWS_REGION',
        'S3_BUCKET_NAME', 'S3_AWS_ACCESS_KEY_ID', 'S3_AWS_SECRET_ACCESS_KEY', 'S3_AWS_REGION',
        'BEDROCK_EMBEDDING_MODEL_ID'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        return False
    
    print("✅ All required environment variables found")
    return True

def test_s3_access():
    """Test S3 access with separate credentials"""
    print("\n🔍 Testing S3 Access (Different Account)...")
    
    try:
        # Create S3 client with S3-specific credentials
        s3_client = boto3.client(
            's3',
            region_name=os.getenv('S3_AWS_REGION'),
            aws_access_key_id=os.getenv('S3_AWS_ACCESS_KEY_ID'),
            aws_secret_access_key=os.getenv('S3_AWS_SECRET_ACCESS_KEY')
        )
        
        bucket_name = os.getenv('S3_BUCKET_NAME')
        
        # Test bucket access
        print(f"  Testing bucket access: {bucket_name}")
        s3_client.head_bucket(Bucket=bucket_name)
        print("  ✅ Bucket access successful")
        
        # List objects
        print("  Testing object listing...")
        response = s3_client.list_objects_v2(Bucket=bucket_name, MaxKeys=10)
        
        if 'Contents' in response:
            object_count = len(response['Contents'])
            print(f"  ✅ Found {object_count} objects in bucket")
            
            # Show first few objects
            for i, obj in enumerate(response['Contents'][:3]):
                print(f"    {i+1}. {obj['Key']} ({obj['Size']} bytes)")
            
            if object_count > 3:
                print(f"    ... and {object_count - 3} more objects")
        else:
            print("  ℹ️  Bucket is empty")
        
        # Test downloading a small object if available
        if 'Contents' in response and response['Contents']:
            test_key = response['Contents'][0]['Key']
            print(f"  Testing object download: {test_key}")
            
            try:
                obj_response = s3_client.get_object(Bucket=bucket_name, Key=test_key)
                content_length = len(obj_response['Body'].read())
                print(f"  ✅ Successfully downloaded object ({content_length} bytes)")
            except Exception as e:
                print(f"  ⚠️  Object download test failed: {e}")
        
        print("✅ S3 access test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ S3 access test failed: {e}")
        return False

def test_bedrock_access():
    """Test Bedrock access with your account credentials"""
    print("\n🔍 Testing Bedrock Access (Your Account)...")
    
    try:
        # Test Bedrock embeddings
        print("  Testing Bedrock embeddings...")
        embeddings = BedrockEmbeddings(
            model_id=os.getenv("BEDROCK_EMBEDDING_MODEL_ID"),
            region_name=os.getenv("AWS_REGION"),
            aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
            aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
        )
        
        # Test embedding generation
        test_text = "This is a test document for embedding generation."
        embedding = embeddings.embed_query(test_text)
        
        if embedding and len(embedding) > 0:
            print(f"  ✅ Embedding generated successfully (dimension: {len(embedding)})")
        else:
            print("  ❌ Embedding generation failed")
            return False
        
        # Test Bedrock LLM if profile ARN is available
        llm_profile = os.getenv("BEDROCK_LLM_PROFILE_ARN")
        if llm_profile:
            print("  Testing Bedrock LLM...")
            
            llm = ChatBedrock(
                model_id=llm_profile,
                region_name=os.getenv("AWS_REGION"),
                aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
                aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
                provider="amazon",
                model_kwargs={"max_tokens": 100, "temperature": 0.1}
            )
            
            # Test LLM invocation
            response = llm.invoke("What is AWS?")
            if response and hasattr(response, 'content') and response.content:
                print(f"  ✅ LLM response generated successfully")
                print(f"    Response preview: {response.content[:100]}...")
            else:
                print("  ❌ LLM response generation failed")
                return False
        else:
            print("  ℹ️  LLM profile ARN not configured, skipping LLM test")
        
        print("✅ Bedrock access test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Bedrock access test failed: {e}")
        return False

def test_aws_utils_integration():
    """Test the AWSClient class with dual account configuration"""
    print("\n🔍 Testing AWSClient Integration...")
    
    try:
        # Import and test AWSClient
        sys.path.append('.')
        from aws_utils import AWSClient
        
        aws_client = AWSClient()
        
        # Test S3 connection
        print("  Testing AWSClient S3 connection...")
        connection_result = aws_client.test_connection()
        
        if connection_result.get('status') == 'success':
            print("  ✅ AWSClient S3 connection successful")
            print(f"    Bucket: {connection_result.get('bucket')}")
            print(f"    Has objects: {connection_result.get('has_objects')}")
        else:
            print("  ❌ AWSClient S3 connection failed")
            return False
        
        # Test file listing
        bucket_name = os.getenv('S3_BUCKET_NAME')
        files = aws_client.list_s3_files(bucket_name)
        print(f"  ✅ Listed {len(files)} files from S3")
        
        print("✅ AWSClient integration test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ AWSClient integration test failed: {e}")
        return False

def main():
    """Main validation function"""
    print("🧪 Dual AWS Account Configuration Validation")
    print("=" * 50)
    
    # Load and validate environment
    if not load_environment():
        sys.exit(1)
    
    # Run tests
    tests = [
        ("Environment Variables", lambda: True),  # Already tested in load_environment
        ("S3 Access", test_s3_access),
        ("Bedrock Access", test_bedrock_access),
        ("AWSClient Integration", test_aws_utils_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your dual AWS account configuration is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check your configuration.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
