#!/bin/bash
# RAG Application Concurrency Fix Script for EC2
# This script fixes critical concurrency issues to prevent database corruption

set -euo pipefail

# Configuration
APP_DIR="/opt/chainlit_rag"
BACKUP_DIR="/opt/chainlit_rag/backups/$(date +%Y%m%d_%H%M%S)"
SERVICES=("rag-api" "rag-frontend")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"; }
error() { echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"; exit 1; }
info() { echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"; }

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root (use sudo)"
    fi
}

# Create backup
create_backup() {
    log "Creating backup of current configuration..."
    mkdir -p "$BACKUP_DIR"
    
    # Backup systemd service files
    for service in "${SERVICES[@]}"; do
        if [[ -f "/etc/systemd/system/${service}.service" ]]; then
            cp "/etc/systemd/system/${service}.service" "$BACKUP_DIR/"
            log "Backed up ${service}.service"
        fi
    done
    
    # Backup vector store if it exists
    if [[ -d "$APP_DIR/vector_store" ]]; then
        cp -r "$APP_DIR/vector_store" "$BACKUP_DIR/"
        log "Backed up vector store"
    fi
    
    log "Backup created at: $BACKUP_DIR"
}

# Stop services safely
stop_services() {
    log "Stopping RAG services..."
    
    for service in "${SERVICES[@]}"; do
        if systemctl is-active --quiet "${service}.service"; then
            log "Stopping ${service}.service..."
            systemctl stop "${service}.service"
            
            # Wait for graceful shutdown
            local timeout=30
            while systemctl is-active --quiet "${service}.service" && [[ $timeout -gt 0 ]]; do
                sleep 1
                ((timeout--))
            done
            
            if systemctl is-active --quiet "${service}.service"; then
                warn "Force stopping ${service}.service..."
                systemctl kill "${service}.service"
                sleep 2
            fi
            
            log "✓ ${service}.service stopped"
        else
            info "${service}.service was not running"
        fi
    done
}

# Fix API service configuration
fix_api_service() {
    log "Fixing RAG API service configuration..."
    
    local service_file="/etc/systemd/system/rag-api.service"
    
    if [[ ! -f "$service_file" ]]; then
        error "RAG API service file not found at $service_file"
    fi
    
    # Create fixed service file
    cat > "$service_file" << 'EOF'
[Unit]
Description=RAG FastAPI Backend Service
Documentation=https://fastapi.tiangolo.com/
After=network.target
Wants=network.target

[Service]
Type=simple
User=raguser
Group=raguser
WorkingDirectory=/opt/chainlit_rag
ExecStart=/opt/chainlit_rag/venv/bin/gunicorn main:app \
    --bind 0.0.0.0:8080 \
    --workers 1 \
    --worker-class uvicorn.workers.UvicornWorker \
    --timeout 300 \
    --keepalive 2 \
    --max-requests 1000 \
    --max-requests-jitter 100 \
    --preload \
    --access-logfile /var/log/rag-api/access.log \
    --error-logfile /var/log/rag-api/error.log \
    --log-level info
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
TimeoutStartSec=60
TimeoutStopSec=30

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Environment
Environment=PYTHONPATH=/opt/chainlit_rag
Environment=PYTHONUNBUFFERED=1

[Install]
WantedBy=multi-user.target
EOF
    
    log "✓ Fixed API service configuration (1 worker)"
}

# Verify vector store integrity
verify_vector_store() {
    log "Verifying vector store integrity..."
    
    local vector_store_path="$APP_DIR/vector_store"
    
    if [[ -d "$vector_store_path" ]]; then
        # Check for lock files that might indicate corruption
        if find "$vector_store_path" -name "*.lock" -type f | grep -q .; then
            warn "Found lock files in vector store - cleaning up..."
            find "$vector_store_path" -name "*.lock" -type f -delete
        fi
        
        # Set proper permissions
        chown -R raguser:raguser "$vector_store_path"
        chmod -R 755 "$vector_store_path"
        
        log "✓ Vector store verified and permissions fixed"
    else
        info "Vector store directory not found - will be created on first use"
    fi
}

# Reload systemd and start services
restart_services() {
    log "Reloading systemd configuration..."
    systemctl daemon-reload
    
    log "Starting services in correct order..."
    
    # Start API service first
    log "Starting RAG API service..."
    systemctl start rag-api.service
    
    # Wait for API to be ready
    local api_ready=false
    local timeout=60
    
    while [[ $timeout -gt 0 ]] && [[ "$api_ready" == false ]]; do
        if curl -s -f http://localhost:8080/health >/dev/null 2>&1; then
            api_ready=true
            log "✓ RAG API is ready"
        else
            sleep 2
            ((timeout-=2))
        fi
    done
    
    if [[ "$api_ready" == false ]]; then
        error "RAG API failed to start within 60 seconds"
    fi
    
    # Start frontend service
    log "Starting RAG Frontend service..."
    systemctl start rag-frontend.service
    sleep 10
    
    # Verify both services are running
    for service in "${SERVICES[@]}"; do
        if systemctl is-active --quiet "${service}.service"; then
            log "✓ ${service}.service is running"
        else
            error "✗ ${service}.service failed to start"
        fi
    done
}

# Test concurrent access
test_concurrent_access() {
    log "Testing concurrent access safety..."
    
    # Test API health endpoint with multiple concurrent requests
    log "Testing API health endpoint..."
    for i in {1..5}; do
        curl -s -f http://localhost:8080/health >/dev/null &
    done
    wait
    log "✓ Concurrent health checks passed"
    
    # Test if vector store is accessible
    if [[ -d "$APP_DIR/vector_store" ]]; then
        log "✓ Vector store is accessible"
    fi
    
    log "✓ Concurrent access test completed"
}

# Show service status
show_status() {
    log "Current Service Status:"
    echo ""
    
    for service in "${SERVICES[@]}"; do
        echo -e "${BLUE}=== ${service}.service ===${NC}"
        systemctl status "${service}.service" --no-pager -l || true
        echo ""
    done
    
    # Show resource usage
    echo -e "${BLUE}=== Resource Usage ===${NC}"
    echo "Memory usage:"
    free -h
    echo ""
    echo "CPU usage:"
    top -bn1 | grep "Cpu(s)" || true
    echo ""
}

# Main execution
main() {
    log "🚀 Starting RAG Application Concurrency Fix"
    log "This will fix critical database corruption issues"
    echo ""
    
    check_root
    create_backup
    stop_services
    fix_api_service
    verify_vector_store
    restart_services
    test_concurrent_access
    
    echo ""
    log "🎉 Concurrency fix completed successfully!"
    echo ""
    info "Summary of changes:"
    info "✓ Reduced API workers from 2 to 1 (prevents Qdrant corruption)"
    info "✓ Verified vector store integrity"
    info "✓ Services restarted with safe configuration"
    info "✓ Concurrent access tested"
    echo ""
    warn "Your application now handles requests sequentially to prevent database corruption."
    warn "For true concurrent access, consider implementing async/await patterns."
    echo ""
    
    show_status
}

# Run main function
main "$@"
