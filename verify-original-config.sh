#!/bin/bash
# Script to verify that the configuration has been reverted to original single AWS account setup

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() { echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"; }
error() { echo -e "${RED}[$(date +'%H:%M:%S')] ERROR: $1${NC}"; }
info() { echo -e "${BLUE}[$(date +'%H:%M:%S')] INFO: $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%H:%M:%S')] WARNING: $1${NC}"; }

APP_DIR="/opt/chainlit_rag"

echo ""
log "🔍 Verifying Original Single AWS Account Configuration"
echo ""

# Check .env file
check_env_file() {
    info "Checking .env file configuration..."
    
    local env_file="$APP_DIR/.env"
    
    if [[ ! -f "$env_file" ]]; then
        error ".env file not found"
        return 1
    fi
    
    # Check for original variables
    if grep -q "^AWS_ACCESS_KEY_ID=AKIAU5LH6BIXI2EVAROB" "$env_file"; then
        log "✓ Original AWS Access Key found"
    else
        error "✗ Original AWS Access Key not found"
        return 1
    fi
    
    if grep -q "^S3_BUCKET_NAME=knowlegde" "$env_file"; then
        log "✓ Original S3 bucket name found"
    else
        error "✗ Original S3 bucket name not found"
        return 1
    fi
    
    # Check that dual account variables are NOT present
    if grep -q "^S3_AWS_ACCESS_KEY_ID=" "$env_file"; then
        error "✗ Dual account S3 credentials still present"
        return 1
    else
        log "✓ No dual account S3 credentials found"
    fi
    
    log "✅ .env file is correctly configured for single AWS account"
    return 0
}

# Check aws_utils.py
check_aws_utils() {
    info "Checking aws_utils.py configuration..."
    
    local aws_utils_file="$APP_DIR/aws_utils.py"
    
    if [[ ! -f "$aws_utils_file" ]]; then
        error "aws_utils.py not found"
        return 1
    fi
    
    # Check for original single account configuration
    if grep -q "self.region = os.getenv('AWS_REGION')" "$aws_utils_file"; then
        log "✓ Original single account region configuration found"
    else
        error "✗ Original single account region configuration not found"
        return 1
    fi
    
    if grep -q "self.access_key = os.getenv('AWS_ACCESS_KEY_ID')" "$aws_utils_file"; then
        log "✓ Original single account access key configuration found"
    else
        error "✗ Original single account access key configuration not found"
        return 1
    fi
    
    # Check that dual account variables are NOT present
    if grep -q "S3_AWS_ACCESS_KEY_ID" "$aws_utils_file"; then
        error "✗ Dual account S3 credentials still present in aws_utils.py"
        return 1
    else
        log "✓ No dual account S3 credentials found in aws_utils.py"
    fi
    
    log "✅ aws_utils.py is correctly configured for single AWS account"
    return 0
}

# Check ingest.py
check_ingest() {
    info "Checking ingest.py configuration..."
    
    local ingest_file="$APP_DIR/ingest.py"
    
    if [[ ! -f "$ingest_file" ]]; then
        error "ingest.py not found"
        return 1
    fi
    
    # Check that dual account comments are removed
    if grep -q "Use Bedrock credentials (your account) for embeddings" "$ingest_file"; then
        warn "⚠ Dual account comment still present in ingest.py"
    else
        log "✓ No dual account comments found in ingest.py"
    fi
    
    log "✅ ingest.py is clean"
    return 0
}

# Check that dual AWS files are removed
check_dual_files_removed() {
    info "Checking that dual AWS account files are removed..."
    
    local dual_files=(
        "update-s3-config.sh"
        "validate-dual-aws-config.py"
        "test-s3-ingestion.sh"
        "update-env-dual-aws.sh"
        "generate-dual-env.sh"
        "DUAL-AWS-ACCOUNT-SETUP.md"
    )
    
    local found_files=()
    
    for file in "${dual_files[@]}"; do
        if [[ -f "$APP_DIR/$file" ]] || [[ -f "$file" ]]; then
            found_files+=("$file")
        fi
    done
    
    if [[ ${#found_files[@]} -eq 0 ]]; then
        log "✅ All dual AWS account files have been removed"
        return 0
    else
        warn "⚠ Some dual AWS account files still exist:"
        for file in "${found_files[@]}"; do
            warn "  - $file"
        done
        return 1
    fi
}

# Test current configuration
test_configuration() {
    info "Testing current configuration..."
    
    # Test if services are running
    if systemctl is-active --quiet rag-api.service; then
        log "✓ RAG API service is running"
    else
        warn "⚠ RAG API service is not running"
    fi
    
    if systemctl is-active --quiet rag-frontend.service; then
        log "✓ RAG Frontend service is running"
    else
        warn "⚠ RAG Frontend service is not running"
    fi
    
    # Test API health if running
    if curl -s -f http://localhost:8080/health >/dev/null 2>&1; then
        log "✓ API health check passed"
    else
        warn "⚠ API health check failed (service may not be running)"
    fi
}

# Show current configuration summary
show_config_summary() {
    echo ""
    log "📋 Current Configuration Summary:"
    echo ""
    echo -e "${BLUE}=== Single AWS Account Configuration ===${NC}"
    echo "AWS_ACCESS_KEY_ID: AKIAU5LH6BIXI2EVAROB"
    echo "AWS_REGION: ap-south-1"
    echo "S3_BUCKET_NAME: knowlegde"
    echo "BEDROCK_EMBEDDING_MODEL_ID: amazon.titan-embed-text-v2:0"
    echo "BEDROCK_LLM_MODEL_ID: amazon.nova-micro-v1:0"
    echo ""
    info "✓ All services (S3, Bedrock) use the same AWS account"
    info "✓ Configuration is back to original state"
    echo ""
}

# Main execution
main() {
    local tests=(
        "Environment File:check_env_file"
        "AWS Utils:check_aws_utils"
        "Ingest File:check_ingest"
        "Dual Files Cleanup:check_dual_files_removed"
        "Service Status:test_configuration"
    )
    
    local passed=0
    local total=${#tests[@]}
    
    for test in "${tests[@]}"; do
        local test_name="${test%:*}"
        local test_func="${test#*:}"
        
        if $test_func; then
            ((passed++))
        fi
        echo ""
    done
    
    show_config_summary
    
    log "📊 Verification Results: $passed/$total checks passed"
    
    if [[ $passed -eq $total ]]; then
        echo ""
        log "🎉 SUCCESS: Configuration successfully reverted to original single AWS account setup!"
        echo ""
        info "Your RAG application is now using:"
        info "✓ Single AWS account for all services"
        info "✓ Original S3 bucket: knowlegde"
        info "✓ Original AWS credentials"
        info "✓ Clean codebase without dual account modifications"
        return 0
    else
        echo ""
        error "⚠ Some verification checks failed. Please review the issues above."
        return 1
    fi
}

# Run main function
main "$@"
