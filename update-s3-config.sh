#!/bin/bash
# Script to update RAG application for different S3 bucket and AWS account
# while keeping existing Bedrock credentials

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"; }
error() { echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"; exit 1; }
info() { echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"; }

# Configuration
APP_DIR="/opt/chainlit_rag"
BACKUP_DIR="/opt/chainlit_rag/backups/s3-config-$(date +%Y%m%d_%H%M%S)"

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root (use sudo)"
    fi
}

# Prompt for new S3 configuration
get_s3_config() {
    log "🔧 S3 Configuration Setup"
    echo ""
    
    # Get new S3 bucket name
    read -p "Enter the new S3 bucket name: " NEW_S3_BUCKET
    if [[ -z "$NEW_S3_BUCKET" ]]; then
        error "S3 bucket name cannot be empty"
    fi
    
    # Get new AWS credentials for S3
    read -p "Enter AWS Access Key ID for S3 account: " NEW_S3_ACCESS_KEY
    if [[ -z "$NEW_S3_ACCESS_KEY" ]]; then
        error "AWS Access Key ID cannot be empty"
    fi
    
    read -s -p "Enter AWS Secret Access Key for S3 account: " NEW_S3_SECRET_KEY
    echo ""
    if [[ -z "$NEW_S3_SECRET_KEY" ]]; then
        error "AWS Secret Access Key cannot be empty"
    fi
    
    # Get AWS region for S3
    read -p "Enter AWS region for S3 bucket (default: ap-south-1): " NEW_S3_REGION
    NEW_S3_REGION=${NEW_S3_REGION:-ap-south-1}
    
    echo ""
    log "Configuration Summary:"
    info "S3 Bucket: $NEW_S3_BUCKET"
    info "S3 Access Key: ${NEW_S3_ACCESS_KEY:0:8}..."
    info "S3 Region: $NEW_S3_REGION"
    echo ""
    
    read -p "Continue with this configuration? (y/N): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        error "Configuration cancelled"
    fi
}

# Create backup
create_backup() {
    log "Creating backup of current configuration..."
    mkdir -p "$BACKUP_DIR"
    
    # Backup .env file
    if [[ -f "$APP_DIR/.env" ]]; then
        cp "$APP_DIR/.env" "$BACKUP_DIR/.env.backup"
        log "Backed up .env file"
    fi
    
    # Backup Python files that might have hardcoded values
    for file in aws_utils.py ingest.py query.py vectorstore_utils.py; do
        if [[ -f "$APP_DIR/$file" ]]; then
            cp "$APP_DIR/$file" "$BACKUP_DIR/"
            log "Backed up $file"
        fi
    done
    
    log "Backup created at: $BACKUP_DIR"
}

# Update environment file
update_env_file() {
    log "Updating environment configuration..."

    local env_file="$APP_DIR/.env"

    # Read current Bedrock configuration
    local current_bedrock_access_key=""
    local current_bedrock_secret_key=""
    local current_bedrock_region=""
    local current_embedding_model=""
    local current_llm_model=""
    local current_llm_profile=""

    if [[ -f "$env_file" ]]; then
        current_bedrock_access_key=$(grep "^AWS_ACCESS_KEY_ID=" "$env_file" | cut -d'=' -f2 || echo "")
        current_bedrock_secret_key=$(grep "^AWS_SECRET_ACCESS_KEY=" "$env_file" | cut -d'=' -f2 || echo "")
        current_bedrock_region=$(grep "^AWS_REGION=" "$env_file" | cut -d'=' -f2 || echo "")
        current_embedding_model=$(grep "^BEDROCK_EMBEDDING_MODEL_ID=" "$env_file" | cut -d'=' -f2 || echo "")
        current_llm_model=$(grep "^BEDROCK_LLM_MODEL_ID=" "$env_file" | cut -d'=' -f2 || echo "")
        current_llm_profile=$(grep "^BEDROCK_LLM_PROFILE_ARN=" "$env_file" | cut -d'=' -f2 || echo "")
    fi

    # Create new .env file with dual account configuration
    cat > "$env_file" << EOF
# AWS Configuration for Bedrock (Your Account)
AWS_ACCESS_KEY_ID=${current_bedrock_access_key}
AWS_SECRET_ACCESS_KEY=${current_bedrock_secret_key}
AWS_REGION=${current_bedrock_region}

# S3 Configuration (Different Account)
S3_BUCKET_NAME=${NEW_S3_BUCKET}
S3_AWS_ACCESS_KEY_ID=${NEW_S3_ACCESS_KEY}
S3_AWS_SECRET_ACCESS_KEY=${NEW_S3_SECRET_KEY}
S3_AWS_REGION=${NEW_S3_REGION}

# Vector Store Configuration
QDRANT_PATH=./vector_store

# Bedrock Configuration (Your Account)
BEDROCK_EMBEDDING_MODEL_ID=${current_embedding_model}
BEDROCK_LLM_MODEL_ID=${current_llm_model}
BEDROCK_LLM_PROFILE_ARN=${current_llm_profile}

# Application Configuration
RAG_API_HOST=0.0.0.0
RAG_API_PORT=8080
API_BASE_URL=http://localhost:8080
CHAINLIT_HOST=0.0.0.0
CHAINLIT_PORT=80
EOF

    # Set proper permissions
    chown raguser:raguser "$env_file"
    chmod 600 "$env_file"

    log "✓ Environment file updated with dual account configuration"
}

# Update Python files for dual account support
update_python_files() {
    log "Updating Python files for dual account support..."

    # Update aws_utils.py if needed
    local aws_utils_file="$APP_DIR/aws_utils.py"
    if [[ -f "$aws_utils_file" ]]; then
        # Check if already updated
        if ! grep -q "S3_AWS_ACCESS_KEY_ID" "$aws_utils_file"; then
            log "Updating aws_utils.py for dual account support..."
            # The file should already be updated by the str-replace-editor
            log "✓ aws_utils.py supports dual account configuration"
        else
            log "✓ aws_utils.py already supports dual account configuration"
        fi
    fi

    log "✓ Python files updated for dual account support"
}

# Test S3 connection
test_s3_connection() {
    log "Testing S3 connection..."
    
    # Create a simple Python test script
    cat > "/tmp/test_s3.py" << 'EOF'
import boto3
import os
from dotenv import load_dotenv

load_dotenv('/opt/chainlit_rag/.env')

try:
    # Test S3 connection with new credentials
    s3_client = boto3.client(
        's3',
        region_name=os.getenv('S3_AWS_REGION'),
        aws_access_key_id=os.getenv('S3_AWS_ACCESS_KEY_ID'),
        aws_secret_access_key=os.getenv('S3_AWS_SECRET_ACCESS_KEY')
    )
    
    bucket_name = os.getenv('S3_BUCKET_NAME')
    
    # Test bucket access
    s3_client.head_bucket(Bucket=bucket_name)
    print(f"✓ Successfully connected to S3 bucket: {bucket_name}")
    
    # List objects
    response = s3_client.list_objects_v2(Bucket=bucket_name, MaxKeys=5)
    if 'Contents' in response:
        print(f"✓ Found {len(response['Contents'])} objects in bucket")
        for obj in response['Contents'][:3]:
            print(f"  - {obj['Key']}")
    else:
        print("ℹ Bucket is empty")
    
    print("✓ S3 connection test successful")
    
except Exception as e:
    print(f"✗ S3 connection test failed: {e}")
    exit(1)
EOF
    
    # Run the test
    cd "$APP_DIR"
    sudo -u raguser venv/bin/python /tmp/test_s3.py
    
    # Clean up
    rm -f /tmp/test_s3.py
    
    log "✓ S3 connection test completed"
}

# Stop services
stop_services() {
    log "Stopping RAG services..."
    
    local services=("rag-api" "rag-frontend")
    
    for service in "${services[@]}"; do
        if systemctl is-active --quiet "${service}.service"; then
            systemctl stop "${service}.service"
            log "Stopped ${service}.service"
        fi
    done
}

# Start services
start_services() {
    log "Starting RAG services..."
    
    # Start API service first
    systemctl start rag-api.service
    sleep 10
    
    # Check if API is ready
    local timeout=30
    while [[ $timeout -gt 0 ]]; do
        if curl -s -f http://localhost:8080/health >/dev/null 2>&1; then
            log "✓ RAG API is ready"
            break
        fi
        sleep 2
        ((timeout-=2))
    done
    
    # Start frontend service
    systemctl start rag-frontend.service
    sleep 5
    
    log "✓ Services started"
}

# Test ingestion
test_ingestion() {
    log "Testing document ingestion with new S3 configuration..."
    
    # Test ingestion endpoint
    local response=$(curl -s -X POST http://localhost:8080/ingest)
    
    if echo "$response" | grep -q '"status":"success"'; then
        log "✓ Document ingestion test successful"
        info "Response: $response"
    else
        warn "Document ingestion test may have issues"
        info "Response: $response"
    fi
}

# Main execution
main() {
    log "🚀 Starting S3 Configuration Update"
    echo ""

    check_root
    get_s3_config
    create_backup
    stop_services
    update_env_file
    update_python_files
    test_s3_connection
    start_services
    test_ingestion

    echo ""
    log "🎉 S3 configuration update completed successfully!"
    echo ""
    info "Summary of changes:"
    info "✓ Updated S3 bucket to: $NEW_S3_BUCKET"
    info "✓ Added separate S3 credentials for different AWS account"
    info "✓ Kept existing Bedrock credentials for your account"
    info "✓ Updated Python files for dual account support"
    info "✓ Tested S3 connection and ingestion"
    echo ""
    warn "Backup created at: $BACKUP_DIR"
    echo ""
}

# Run main function
main "$@"
