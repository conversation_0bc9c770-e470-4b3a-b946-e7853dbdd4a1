import os
from typing import List, Dict, Optional
from langchain_aws import BedrockEmbeddings
from langchain.schema import Document
from langchain.vectorstores.base import VectorStore
from langchain_community.vectorstores.utils import maximal_marginal_relevance
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue
import logging

logger = logging.getLogger(__name__)

COLLECTION_NAME = "documents"


class QdrantVectorStoreWrapper(VectorStore):
    """
    LangChain-compatible wrapper for Qdrant vector store.
    Provides retriever interface while maintaining compatibility with existing code.
    """

    def __init__(self, client: QdrantClient, embeddings: BedrockEmbeddings, collection_name: str = COLLECTION_NAME):
        self.client = client
        self._embeddings = embeddings
        self.collection_name = collection_name

    @property
    def embeddings(self):
        return self._embeddings

    @embeddings.setter
    def embeddings(self, value):
        self._embeddings = value

    def add_texts(self, texts: List[str], metadatas: Optional[List[Dict]] = None) -> List[str]:
        """Add texts to the vector store."""
        if metadatas is None:
            metadatas = [{} for _ in texts]

        points = []
        ids = []

        for i, (text, metadata) in enumerate(zip(texts, metadatas)):
            # Generate embedding
            vector = self.embeddings.embed_query(text)

            # Generate ID if not provided
            point_id = metadata.get('id', f"doc_{i}_{hash(text) % 1000000}")
            ids.append(point_id)

            # Create payload
            payload = {
                "content": text,
                "source": metadata.get("source", "unknown"),
                "id": point_id,
                **metadata
            }

            points.append(PointStruct(id=point_id, vector=vector, payload=payload))

        # Upsert points
        self.client.upsert(collection_name=self.collection_name, points=points)
        return ids

    def similarity_search(self, query: str, k: int = 4, filter: Optional[Dict] = None) -> List[Document]:
        """Perform similarity search and return documents."""
        return self.similarity_search_with_score(query, k, filter)

    def similarity_search_with_score(self, query: str, k: int = 4, filter: Optional[Dict] = None) -> List[Document]:
        """Perform similarity search and return documents with scores."""
        # Generate query vector
        query_vector = self.embeddings.embed_query(query)

        # Build filter if provided
        qdrant_filter = None
        if filter:
            conditions = []
            for key, value in filter.items():
                conditions.append(FieldCondition(key=key, match=MatchValue(value=value)))
            if conditions:
                qdrant_filter = Filter(must=conditions)

        # Perform search
        search_result = self.client.search(
            collection_name=self.collection_name,
            query_vector=query_vector,
            limit=k,
            with_payload=True,
            query_filter=qdrant_filter
        )

        # Convert to LangChain documents
        documents = []
        for hit in search_result:
            payload = hit.payload or {}
            content = payload.get("content", "")
            metadata = {
                "source": payload.get("source", "unknown"),
                "score": float(hit.score),
                "id": payload.get("id", "unknown")
            }
            # Add other metadata fields
            for key, value in payload.items():
                if key not in ["content", "source", "id"]:
                    metadata[key] = value

            documents.append(Document(page_content=content, metadata=metadata))

        return documents

    def similarity_search_by_vector(self, embedding: List[float], k: int = 4, filter: Optional[Dict] = None) -> List[Document]:
        """Perform similarity search using a vector."""
        # Build filter if provided
        qdrant_filter = None
        if filter:
            conditions = []
            for key, value in filter.items():
                conditions.append(FieldCondition(key=key, match=MatchValue(value=value)))
            if conditions:
                qdrant_filter = Filter(must=conditions)

        # Perform search
        search_result = self.client.search(
            collection_name=self.collection_name,
            query_vector=embedding,
            limit=k,
            with_payload=True,
            query_filter=qdrant_filter
        )

        # Convert to LangChain documents
        documents = []
        for hit in search_result:
            payload = hit.payload or {}
            content = payload.get("content", "")
            metadata = {
                "source": payload.get("source", "unknown"),
                "score": float(hit.score),
                "id": payload.get("id", "unknown")
            }
            documents.append(Document(page_content=content, metadata=metadata))

        return documents

    def max_marginal_relevance_search(self, query: str, k: int = 4, fetch_k: int = 20, lambda_mult: float = 0.5, filter: Optional[Dict] = None) -> List[Document]:
        """Perform MMR search for diverse results."""
        # Get more documents than needed
        docs = self.similarity_search_with_score(query, fetch_k, filter)

        if not docs:
            return []

        # Extract embeddings and documents
        embeddings = []
        documents = []
        for doc in docs:
            # Re-embed the document content for MMR calculation
            embedding = self.embeddings.embed_query(doc.page_content)
            embeddings.append(embedding)
            documents.append(doc)

        # Get query embedding
        query_embedding = self.embeddings.embed_query(query)

        # Apply MMR
        mmr_indices = maximal_marginal_relevance(
            query_embedding, embeddings, lambda_mult=lambda_mult, k=k
        )

        return [documents[i] for i in mmr_indices]

    def delete(self, ids: Optional[List[str]] = None) -> Optional[bool]:
        """Delete documents by IDs."""
        if ids:
            self.client.delete(
                collection_name=self.collection_name,
                points_selector=ids
            )
            return True
        return False

    @classmethod
    def from_texts(cls, texts: List[str], embedding: BedrockEmbeddings, metadatas: Optional[List[Dict]] = None, **kwargs) -> "QdrantVectorStoreWrapper":
        """Create vector store from texts."""
        # Initialize client
        qdrant_path = kwargs.get("qdrant_path", os.getenv("QDRANT_PATH", "./vector_store"))
        collection_name = kwargs.get("collection_name", COLLECTION_NAME)

        os.makedirs(qdrant_path, exist_ok=True)
        client = QdrantClient(path=qdrant_path)

        # Create collection
        client.recreate_collection(
            collection_name=collection_name,
            vectors_config=VectorParams(size=1024, distance=Distance.COSINE)  # Using 1024 as per current implementation
        )

        # Create wrapper
        wrapper = cls(client, embedding, collection_name)

        # Add texts
        wrapper.add_texts(texts, metadatas)

        return wrapper

def get_bedrock_embeddings():
    return BedrockEmbeddings(
        model_id=os.getenv("BEDROCK_EMBEDDING_MODEL_ID"),
        region_name=os.getenv("AWS_REGION"),
        aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
        aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
    )

def build_vectorstore(docs):
    """
    Build vector store using the new LangChain-compatible wrapper.
    Maintains backward compatibility with existing code.
    """
    qdrant_path = os.getenv("QDRANT_PATH", "./vector_store")
    os.makedirs(qdrant_path, exist_ok=True)
    client = QdrantClient(path=qdrant_path)
    embeddings = get_bedrock_embeddings()

    # Always recreate the collection to ensure correct vector size
    client.recreate_collection(
        collection_name=COLLECTION_NAME,
        vectors_config=VectorParams(size=1024, distance=Distance.COSINE)
    )

    # Create wrapper
    wrapper = QdrantVectorStoreWrapper(client, embeddings, COLLECTION_NAME)

    # Extract texts and metadata from documents
    texts = [doc.page_content for doc in docs]
    metadatas = [doc.metadata for doc in docs]

    # Add documents to vector store
    wrapper.add_texts(texts, metadatas)

    logger.info(f"[Qdrant] Upserted {len(docs)} documents to vector store")

    # Return the raw client for backward compatibility
    return client

# Global client instance for singleton pattern
_global_client = None

def load_vectorstore():
    """
    Load vector store and return LangChain-compatible wrapper with singleton client.
    """
    global _global_client

    if _global_client is None:
        qdrant_path = os.getenv("QDRANT_PATH", "./vector_store")

        try:
            _global_client = QdrantClient(path=qdrant_path)
        except RuntimeError as e:
            if "already accessed by another instance" in str(e):
                logger.warning(f"Qdrant storage locked, using in-memory client: {e}")
                _global_client = QdrantClient(":memory:")
            else:
                raise e

    embeddings = get_bedrock_embeddings()

    # Return wrapper for LangChain compatibility
    return QdrantVectorStoreWrapper(_global_client, embeddings, COLLECTION_NAME)

def load_vectorstore_client():
    """
    Load raw Qdrant client for backward compatibility using singleton pattern.
    """
    global _global_client

    if _global_client is None:
        qdrant_path = os.getenv("QDRANT_PATH", "./vector_store")

        try:
            _global_client = QdrantClient(path=qdrant_path)
        except RuntimeError as e:
            if "already accessed by another instance" in str(e):
                logger.warning(f"Qdrant storage locked, using in-memory client: {e}")
                _global_client = QdrantClient(":memory:")
            else:
                raise e

    return _global_client
