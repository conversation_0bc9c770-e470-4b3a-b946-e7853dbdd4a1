from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, Optional, Union
from dotenv import load_dotenv
from ingest import DocumentIngester
from query import QueryEngine
from fastapi_mcp import FastApiMCP
import uvicorn
from fastapi.middleware.cors import CORSMiddleware
import socket

load_dotenv()

app = FastAPI(
    title="RAG API",
    description="Retrieval-Augmented Generation API using LangChain and AWS services",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

mcp = FastApiMCP(app)
mcp.mount()

doc_ingester = DocumentIngester()
query_engine = None

class AdvancedQueryRequest(BaseModel):
    question: str
    retrieval_config: Optional[Dict[str, Any]] = None

class IngestResponse(BaseModel):
    status: str
    message: str
    chunks: Union[int, str]

@app.get("/")
async def root():
    return {"message": "Welcome to RAG API"}

@app.post("/ingest", response_model=IngestResponse)
async def ingest_documents():
    result = doc_ingester.process_s3_documents()
    
    if result["status"] == "error":
        raise HTTPException(status_code=500, detail=result["message"])
    
    if isinstance(result["chunks"], int):
        result["chunks"] = str(result["chunks"])
    
    return result


@app.get("/health")
async def health_check():
    return {"status": "healthy"}

def get_query_engine():
    global query_engine
    if query_engine is None:
        query_engine = QueryEngine()
    return query_engine

@app.post("/query/advanced")
async def advanced_query_endpoint(request: AdvancedQueryRequest):
    engine = get_query_engine()
    return engine.query_advanced(
        question=request.question,
        retrieval_config=request.retrieval_config
    )

def find_available_port(start_port: int = 8888, max_attempts: int = 10) -> int:
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('', port))
                return port
        except OSError:
            continue
    raise RuntimeError(f"Could not find an available port after {max_attempts} attempts")

if __name__ == "__main__":
    port = find_available_port()
    uvicorn.run(app, host="0.0.0.0", port=port)