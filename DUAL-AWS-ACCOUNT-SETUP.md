# 🔄 Dual AWS Account Configuration Guide

## Overview

This guide helps you configure your RAG application to use:
- **Different AWS Account for S3**: Document storage and ingestion
- **Your AWS Account for Bedrock**: LLM and embedding models

## 📋 Prerequisites

1. **S3 Account Access**:
   - AWS Access Key ID for S3 account
   - AWS Secret Access Key for S3 account
   - S3 bucket name
   - AWS region where S3 bucket is located

2. **Your Bedrock Account** (keep existing):
   - AWS Access Key ID for your account
   - AWS Secret Access Key for your account
   - Bedrock model IDs and profile ARNs

## 🚀 Quick Setup

### Step 1: Upload Configuration Scripts

```bash
# Upload scripts to your EC2 instance
scp update-s3-config.sh ec2-user@YOUR-EC2-IP:/tmp/
scp validate-dual-aws-config.py ec2-user@YOUR-EC2-IP:/tmp/
```

### Step 2: Run the Configuration Update

```bash
# SSH into your EC2 instance
ssh ec2-user@YOUR-EC2-IP

# Make script executable
chmod +x /tmp/update-s3-config.sh

# Run the configuration update
sudo /tmp/update-s3-config.sh
```

The script will prompt you for:
- New S3 bucket name
- S3 AWS Access Key ID
- S3 AWS Secret Access Key
- S3 AWS region

### Step 3: Validate Configuration

```bash
# Copy validation script to app directory
sudo cp /tmp/validate-dual-aws-config.py /opt/chainlit_rag/

# Run validation
cd /opt/chainlit_rag
sudo -u raguser venv/bin/python validate-dual-aws-config.py
```

## 📁 Files Modified

### 1. Environment Configuration (`.env`)

**Before:**
```env
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=AFkXvxcW0qvedmm5CrwQae3zW0j9V4wrZ0GT0Lqj
AWS_REGION=ap-south-1
S3_BUCKET_NAME=knowlegde
```

**After:**
```env
# AWS Configuration for Bedrock (Your Account)
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=AFkXvxcW0qvedmm5CrwQae3zW0j9V4wrZ0GT0Lqj
AWS_REGION=ap-south-1

# S3 Configuration (Different Account)
S3_BUCKET_NAME=new-bucket-name
S3_AWS_ACCESS_KEY_ID=AKIA...
S3_AWS_SECRET_ACCESS_KEY=...
S3_AWS_REGION=us-east-1
```

### 2. AWS Utils (`aws_utils.py`)

Updated `AWSClient` class to support dual credentials:
- S3 operations use `S3_AWS_*` credentials
- Bedrock operations use `AWS_*` credentials

### 3. Ingestion (`ingest.py`)

Embeddings continue to use your Bedrock account credentials.

## 🔍 Validation Tests

The validation script tests:

1. **✅ Environment Variables**: All required variables present
2. **✅ S3 Access**: Connection, listing, and download from new bucket
3. **✅ Bedrock Access**: Embedding generation and LLM responses
4. **✅ Integration**: AWSClient class with dual account support

## 🛠️ Manual Configuration (Alternative)

If you prefer manual setup:

### 1. Update `.env` File

```bash
sudo nano /opt/chainlit_rag/.env
```

Add the S3-specific variables:
```env
# S3 Configuration (Different Account)
S3_BUCKET_NAME=your-new-bucket
S3_AWS_ACCESS_KEY_ID=your-s3-access-key
S3_AWS_SECRET_ACCESS_KEY=your-s3-secret-key
S3_AWS_REGION=your-s3-region
```

### 2. Update `aws_utils.py`

The `AWSClient` class has been updated to use:
- `S3_AWS_*` variables for S3 operations
- `AWS_*` variables for Bedrock operations

### 3. Restart Services

```bash
sudo systemctl restart rag-api.service rag-frontend.service
```

## 🧪 Testing

### Test S3 Access

```bash
# Test S3 connection
curl -X POST http://localhost:8080/ingest
```

### Test Query (Bedrock)

```bash
# Test query endpoint
curl -X POST http://localhost:8080/query/advanced \
  -H "Content-Type: application/json" \
  -d '{"question": "What is AWS?"}'
```

## 🔧 Troubleshooting

### Common Issues

1. **S3 Access Denied**
   - Verify S3 credentials are correct
   - Check bucket permissions
   - Ensure bucket exists in specified region

2. **Bedrock Access Issues**
   - Verify your Bedrock credentials are unchanged
   - Check Bedrock model availability in your region

3. **Service Restart Issues**
   - Check logs: `sudo journalctl -u rag-api.service -f`
   - Verify environment file permissions: `ls -la /opt/chainlit_rag/.env`

### Debug Commands

```bash
# Check environment variables
sudo -u raguser bash -c 'cd /opt/chainlit_rag && source venv/bin/activate && python -c "import os; from dotenv import load_dotenv; load_dotenv(); print(f\"S3 Bucket: {os.getenv(\"S3_BUCKET_NAME\")}\"); print(f\"S3 Region: {os.getenv(\"S3_AWS_REGION\")}\"); print(f\"Bedrock Region: {os.getenv(\"AWS_REGION\")}\")"'

# Test S3 connection manually
sudo -u raguser bash -c 'cd /opt/chainlit_rag && source venv/bin/activate && python validate-dual-aws-config.py'

# Check service status
sudo systemctl status rag-api.service
```

## 📊 Configuration Summary

| Component | Account | Credentials Used |
|-----------|---------|------------------|
| **S3 Operations** | Different Account | `S3_AWS_ACCESS_KEY_ID`, `S3_AWS_SECRET_ACCESS_KEY` |
| **Bedrock LLM** | Your Account | `AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY` |
| **Bedrock Embeddings** | Your Account | `AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY` |
| **Vector Store** | Local | N/A |

## ✅ Success Indicators

After successful configuration:

1. **✅ S3 ingestion works** with new bucket
2. **✅ Query responses** use your Bedrock models
3. **✅ No authentication errors** in logs
4. **✅ Validation script passes** all tests

---

**🎯 This setup allows you to use documents from any S3 bucket while keeping your Bedrock models secure in your own account.**
