#!/bin/bash
# Test script for S3 ingestion with dual AWS account configuration

set -euo pipefail

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() { echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"; }
error() { echo -e "${RED}[$(date +'%H:%M:%S')] ERROR: $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%H:%M:%S')] WARNING: $1${NC}"; }
info() { echo -e "${BLUE}[$(date +'%H:%M:%S')] INFO: $1${NC}"; }

# Configuration
API_URL="http://localhost:8080"
APP_DIR="/opt/chainlit_rag"

# Check if services are running
check_services() {
    log "Checking service status..."
    
    local services=("rag-api" "rag-frontend")
    
    for service in "${services[@]}"; do
        if systemctl is-active --quiet "${service}.service"; then
            log "✓ ${service}.service is running"
        else
            error "✗ ${service}.service is not running"
            return 1
        fi
    done
}

# Test API health
test_api_health() {
    log "Testing API health..."
    
    local response=$(curl -s -w "%{http_code}" "$API_URL/health" -o /tmp/health_response.json)
    
    if [[ "$response" == "200" ]]; then
        log "✓ API health check passed"
        return 0
    else
        error "✗ API health check failed (HTTP $response)"
        return 1
    fi
}

# Check environment configuration
check_environment() {
    log "Checking environment configuration..."
    
    local env_file="$APP_DIR/.env"
    
    if [[ ! -f "$env_file" ]]; then
        error "Environment file not found: $env_file"
        return 1
    fi
    
    # Check for dual account configuration
    local required_vars=("S3_BUCKET_NAME" "S3_AWS_ACCESS_KEY_ID" "S3_AWS_SECRET_ACCESS_KEY" "AWS_ACCESS_KEY_ID" "AWS_SECRET_ACCESS_KEY")
    
    for var in "${required_vars[@]}"; do
        if grep -q "^${var}=" "$env_file"; then
            log "✓ Found environment variable: $var"
        else
            error "✗ Missing environment variable: $var"
            return 1
        fi
    done
    
    # Check S3 bucket name
    local s3_bucket=$(grep "^S3_BUCKET_NAME=" "$env_file" | cut -d'=' -f2)
    info "S3 Bucket configured: $s3_bucket"
    
    # Check if using separate S3 credentials
    local s3_access_key=$(grep "^S3_AWS_ACCESS_KEY_ID=" "$env_file" | cut -d'=' -f2 || echo "")
    local bedrock_access_key=$(grep "^AWS_ACCESS_KEY_ID=" "$env_file" | cut -d'=' -f2 || echo "")
    
    if [[ -n "$s3_access_key" && "$s3_access_key" != "$bedrock_access_key" ]]; then
        log "✓ Using separate credentials for S3 and Bedrock"
    elif [[ -n "$s3_access_key" && "$s3_access_key" == "$bedrock_access_key" ]]; then
        warn "⚠ Using same credentials for S3 and Bedrock"
    else
        info "Using fallback credentials (same account)"
    fi
}

# Test S3 connection
test_s3_connection() {
    log "Testing S3 connection..."
    
    # Run validation script if available
    if [[ -f "$APP_DIR/validate-dual-aws-config.py" ]]; then
        log "Running comprehensive validation..."
        cd "$APP_DIR"
        if sudo -u raguser venv/bin/python validate-dual-aws-config.py; then
            log "✓ S3 connection validation passed"
            return 0
        else
            error "✗ S3 connection validation failed"
            return 1
        fi
    else
        warn "Validation script not found, skipping detailed S3 test"
        return 0
    fi
}

# Test document ingestion
test_ingestion() {
    log "Testing document ingestion..."
    
    # Call ingestion endpoint
    log "Calling ingestion endpoint..."
    local response=$(curl -s -w "%{http_code}" -X POST "$API_URL/ingest" -o /tmp/ingest_response.json)
    local http_code="${response: -3}"
    
    if [[ "$http_code" == "200" ]]; then
        log "✓ Ingestion endpoint responded successfully"
        
        # Parse response
        local status=$(jq -r '.status' /tmp/ingest_response.json 2>/dev/null || echo "unknown")
        local message=$(jq -r '.message' /tmp/ingest_response.json 2>/dev/null || echo "unknown")
        local chunks=$(jq -r '.chunks' /tmp/ingest_response.json 2>/dev/null || echo "unknown")
        
        info "Ingestion Status: $status"
        info "Message: $message"
        info "Chunks processed: $chunks"
        
        if [[ "$status" == "success" ]]; then
            log "✓ Document ingestion completed successfully"
            return 0
        else
            error "✗ Document ingestion failed: $message"
            return 1
        fi
    else
        error "✗ Ingestion endpoint failed (HTTP $http_code)"
        cat /tmp/ingest_response.json 2>/dev/null || echo "No response body"
        return 1
    fi
}

# Test query functionality
test_query() {
    log "Testing query functionality..."
    
    # Test query endpoint
    local query_data='{"question": "What is AWS?"}'
    local response=$(curl -s -w "%{http_code}" -X POST "$API_URL/query/advanced" \
        -H "Content-Type: application/json" \
        -d "$query_data" \
        -o /tmp/query_response.json)
    
    local http_code="${response: -3}"
    
    if [[ "$http_code" == "200" ]]; then
        log "✓ Query endpoint responded successfully"
        
        # Check if response contains answer
        if jq -e '.answer' /tmp/query_response.json >/dev/null 2>&1; then
            local answer=$(jq -r '.answer' /tmp/query_response.json | head -c 100)
            info "Query response preview: $answer..."
            log "✓ Query functionality working"
            return 0
        else
            warn "⚠ Query response format unexpected"
            return 1
        fi
    else
        error "✗ Query endpoint failed (HTTP $http_code)"
        return 1
    fi
}

# Clean up temporary files
cleanup() {
    rm -f /tmp/health_response.json /tmp/ingest_response.json /tmp/query_response.json
}

# Main test function
main() {
    echo ""
    log "🧪 Starting S3 Ingestion Test with Dual AWS Account Configuration"
    echo ""
    
    # Set up cleanup trap
    trap cleanup EXIT
    
    # Run tests
    local tests=(
        "Service Status:check_services"
        "API Health:test_api_health"
        "Environment Config:check_environment"
        "S3 Connection:test_s3_connection"
        "Document Ingestion:test_ingestion"
        "Query Functionality:test_query"
    )
    
    local passed=0
    local total=${#tests[@]}
    
    for test in "${tests[@]}"; do
        local test_name="${test%:*}"
        local test_func="${test#*:}"
        
        echo ""
        info "Running: $test_name"
        
        if $test_func; then
            ((passed++))
        else
            error "Test failed: $test_name"
        fi
    done
    
    echo ""
    log "📊 Test Results: $passed/$total tests passed"
    
    if [[ $passed -eq $total ]]; then
        echo ""
        log "🎉 All tests passed! Your dual AWS account configuration is working correctly."
        echo ""
        info "✓ S3 ingestion works with different AWS account"
        info "✓ Bedrock queries work with your AWS account"
        info "✓ Application is ready for production use"
        return 0
    else
        echo ""
        error "⚠ Some tests failed. Please check the configuration and logs."
        echo ""
        info "Debug commands:"
        info "  sudo journalctl -u rag-api.service -f"
        info "  sudo -u raguser bash -c 'cd $APP_DIR && source venv/bin/activate && python validate-dual-aws-config.py'"
        return 1
    fi
}

# Check if jq is available
if ! command -v jq &> /dev/null; then
    warn "jq not available - JSON parsing will be limited"
fi

# Run main function
main "$@"
