from typing import Dict, Any
from langchain_aws import BedrockEmbeddings, ChatBedrock
from vectorstore_utils import load_vectorstore, load_vectorstore_client
from prompts import AWSPromptSelector, AWSResponseParser, validate_prompt_inputs, extract_aws_services
from advanced_retrieval import Hybrid<PERSON><PERSON><PERSON><PERSON>riever, ConfigurableRetriever
from token_utils import get_token_tracker, extract_token_usage, TokenUsage
import os

class QueryEngine:
    def __init__(self):
        self.vector_store = load_vectorstore()
        self.client = load_vectorstore_client()

        self.embeddings = BedrockEmbeddings(
            model_id=os.getenv("BEDROCK_EMBEDDING_MODEL_ID"),
            region_name=os.getenv("AWS_REGION"),
            aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
            aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
        )

        self.prompt_selector = AWSPromptSelector()
        self.response_parser = AWSResponseParser()

        # Initialize token tracker
        self.token_tracker = get_token_tracker()

        profile_arn = os.getenv("BEDROCK_LLM_PROFILE_ARN")

        if profile_arn:
            llm_kwargs = dict(
                model_id=profile_arn,
                region_name=os.getenv("AWS_REGION"),
                aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
                aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
                provider="amazon",
                model_kwargs={"max_tokens": 1500, "temperature": 0.4}
            )
        else:
            llm_kwargs = dict(
                model_id=os.getenv("BEDROCK_LLM_MODEL_ID", "amazon.nova-micro-v1:0"),
                region_name=os.getenv("AWS_REGION"),
                aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
                aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
                model_kwargs={"max_tokens": 1500, "temperature": 0.4}
            )

        self.llm = ChatBedrock(**llm_kwargs)
        self._initialize_advanced_retrieval()

    def _initialize_advanced_retrieval(self):
        self.hybrid_retriever = HybridAWSRetriever(
            vectorstore=self.vector_store,
            weights=[0.7, 0.3],
            k=8
        )

        aws_config = {
            "similarity_threshold": 0.3,
            "max_results": 8,
            "use_hybrid": True,
            "hybrid_weights": [0.7, 0.3],
            "use_mmr": False,
            "mmr_lambda": 0.5,
            "aws_boost": True,
            "filter_duplicates": True
        }

        self.configurable_retriever = ConfigurableRetriever(
            vectorstore=self.vector_store,
            llm=self.llm,
            config=aws_config
        )

    def get_architecture_diagrams(self, question: str = None) -> Dict[str, Any]:
        """
        Retrieve architecture diagrams from the knowledge base.
        
        Args:
            question: Optional question to contextualize the search
            
        Returns:
            Dict containing image data and metadata
        """
        # Define search queries for architecture diagrams
        base_query = "architecture diagram"
        if question:
            query = f"{question} {base_query}"
        else:
            query = base_query
            
        # Filter for documents that are images and contain architecture diagrams
        documents = self.vector_store.similarity_search(
            query, 
            k=10,
            filter={"is_image": True}
        )
        
        # Extract image data from the documents
        images = []
        for doc in documents:
            if doc.metadata.get("is_image", False) and "base64_image" in doc.metadata:
                images.append({
                    "id": doc.metadata.get("source", "unknown"),
                    "base64": doc.metadata.get("base64_image"),
                    "format": doc.metadata.get("image_format", "PNG"),
                    "caption": doc.metadata.get("image_caption", "Architecture Diagram"),
                    "source_document": doc.metadata.get("file_path", "unknown"),
                    "page_number": doc.metadata.get("page_number")
                })
        
        return {
            "images": images,
            "count": len(images),
            "query": query
        }

    def query_advanced(self, question: str, retrieval_config: Dict[str, Any] = None) -> Dict[str, Any]:
        # Check if this is a request for architecture diagrams
        is_diagram_request = any(term in question.lower() for term in [
            "diagram", "architecture diagram", "visual", "picture", "image", "show me"
        ])
        
        # Process normally first
        validation_result = validate_prompt_inputs(question, "")
        if not validation_result["is_valid"]:
            return {
                "answer": f"Invalid input: {', '.join(validation_result['errors'])}",
                "sources": [],
                "validation_errors": validation_result["errors"]
            }

        sanitized_question = validation_result["sanitized_question"]

        if self.configurable_retriever:
            documents = self.configurable_retriever.retrieve(
                query=sanitized_question,
                **(retrieval_config or {})
            )
        elif self.hybrid_retriever:
            documents = self.hybrid_retriever.get_relevant_documents(sanitized_question)
        else:
            documents = self.vector_store.similarity_search(sanitized_question, k=8)

        if not documents:
            return {
                "answer": "No relevant information found in the knowledge base.",
                "sources": [],
                "query_type": "no_results"
            }

        context_chunks = []
        sources = []
        
        # Check for images in the documents
        images = []
        for doc in documents:
            if doc.metadata.get("is_image", False) and "base64_image" in doc.metadata:
                images.append({
                    "id": doc.metadata.get("source", "unknown"),
                    "base64": doc.metadata.get("base64_image"),
                    "format": doc.metadata.get("image_format", "PNG"),
                    "caption": doc.metadata.get("image_caption", "Image"),
                    "source_document": doc.metadata.get("file_path", "unknown"),
                    "page_number": doc.metadata.get("page_number")
                })
            
            context_chunks.append(doc.page_content)
            sources.append({
                "source": doc.metadata.get("source", "unknown"),
                "score": doc.metadata.get("score", 0.0),
                "content_preview": doc.page_content[:200] + "..." if len(doc.page_content) > 200 else doc.page_content,
                "id": doc.metadata.get("id", "unknown"),
                "bm25_score": doc.metadata.get("bm25_score"),
                "aws_boost": doc.metadata.get("aws_boost")
            })

        # If this is a request for diagrams and no images were found in the regular search,
        # specifically search for images
        if is_diagram_request and not images:
            diagram_results = self.get_architecture_diagrams(sanitized_question)
            images = diagram_results.get("images", [])

        context = "\n---\n".join(context_chunks)

        # Validate context and update validation result
        context_validation = validate_prompt_inputs(sanitized_question, context)

        selected_template = self.prompt_selector.select_template(sanitized_question, context)
        formatted_prompt = selected_template.format_messages(
            question=sanitized_question,
            context=context
        )

        # Invoke LLM and track token usage
        response = self.llm.invoke(formatted_prompt)
        raw_answer = response.content if hasattr(response, 'content') else str(response)

        # Extract token usage from the response
        model_name = getattr(self.llm, 'model_id', 'unknown')
        token_usage = extract_token_usage(response, model_name)

        # If no token usage metadata is available, estimate from text
        if token_usage.input_tokens == 0 and token_usage.output_tokens == 0:
            # Estimate input tokens from the formatted prompt
            prompt_text = ""
            if hasattr(formatted_prompt, '__iter__'):
                for msg in formatted_prompt:
                    if hasattr(msg, 'content'):
                        prompt_text += msg.content + " "
            else:
                prompt_text = str(formatted_prompt)

            token_usage = self.token_tracker.create_token_usage_from_text(
                input_text=prompt_text,
                output_text=raw_answer,
                model_name=model_name
            )

        parsed_response = self.response_parser.parse(raw_answer)
        query_type = self._classify_query_type(sanitized_question)

        # Extract AWS services mentioned
        aws_services = extract_aws_services(context + " " + raw_answer)

        # Determine query characteristics
        query_characteristics = self._analyze_query_characteristics(sanitized_question, context)

        # For diagram requests, modify the answer to mention the images
        if is_diagram_request and images:
            image_info = f"\n\nI've found {len(images)} architecture diagram(s) in the documents that might be relevant to your query."
            parsed_response["answer"] += image_info

        return {
            "answer": parsed_response["answer"],
            "sources": sources,
            "query_type": query_type,
            "confidence": parsed_response.get("confidence"),
            "key_findings": parsed_response.get("key_findings", []),
            "has_aws_sop_content": parsed_response.get("has_aws_sop_content", False),
            "has_aws_content": parsed_response.get("has_aws_content", False),
            "aws_services_mentioned": aws_services,
            "query_characteristics": query_characteristics,
            "validation_warnings": validation_result.get("warnings", []) + context_validation.get("warnings", []),
            "retrieval_method": "advanced_configurable",
            "template_used": query_type,
            "retrieval_config": retrieval_config or {},
            "context_quality": self._assess_context_quality(context, sanitized_question),
            "images": images,
            "has_images": len(images) > 0,
            "token_usage": token_usage.to_dict()
        }

    def _classify_query_type(self, question: str) -> str:
        """
        Classify the query type based on keywords and content.

        Args:
            question: User question to classify

        Returns:
            str: Query type classification
        """
        question_lower = question.lower()

        # Define keyword patterns for different query types
        evaluation_keywords = ["evaluate", "assess", "analyze", "review", "compare", "architecture"]
        service_keywords = ["configure", "setup", "install", "deploy", "ec2", "s3", "rds", "lambda"]
        comparison_keywords = ["compare", "versus", "vs", "difference", "better", "choose"]
        general_keywords = ["how", "what", "why", "when", "procedure", "process", "sop"]

        # Score each category
        evaluation_score = sum(1 for keyword in evaluation_keywords if keyword in question_lower)
        service_score = sum(1 for keyword in service_keywords if keyword in question_lower)
        comparison_score = sum(1 for keyword in comparison_keywords if keyword in question_lower)
        general_score = sum(1 for keyword in general_keywords if keyword in question_lower)

        # Determine query type based on highest score
        scores = {
            "architecture_evaluation": evaluation_score,
            "service_analysis": service_score,
            "service_comparison": comparison_score,
            "general_aws": general_score
        }

        # Return the type with highest score, default to general_aws
        max_score = max(scores.values())
        if max_score == 0:
            return "general_aws"

        return max(scores, key=scores.get)

    def _analyze_query_characteristics(self, question: str, context: str) -> Dict[str, Any]:
        """
        Analyze query characteristics for enhanced insights.

        Args:
            question: User question
            context: Retrieved context

        Returns:
            Dict containing query analysis
        """
        question_lower = question.lower()
        context_lower = context.lower()

        characteristics = {
            "is_security_focused": any(term in question_lower for term in [
                "security", "iam", "permission", "access", "encrypt", "mfa", "auth"
            ]),
            "is_configuration_focused": any(term in question_lower for term in [
                "configure", "setup", "install", "deploy", "config"
            ]),
            "is_cost_focused": any(term in question_lower for term in [
                "cost", "billing", "price", "budget", "optimize", "save"
            ]),
            "is_compliance_focused": any(term in question_lower for term in [
                "compliance", "audit", "regulation", "standard", "policy"
            ]),
            "is_troubleshooting": any(term in question_lower for term in [
                "error", "issue", "problem", "troubleshoot", "debug", "fix"
            ]),
            "has_procedural_context": any(term in context_lower for term in [
                "step", "procedure", "process", "workflow", "guideline"
            ]),
            "complexity_level": self._assess_complexity(question)
        }

        return characteristics

    def _assess_complexity(self, question: str) -> str:
        """
        Assess the complexity level of a question.

        Args:
            question: User question

        Returns:
            str: Complexity level (low, medium, high)
        """
        question_lower = question.lower()

        # High complexity indicators
        high_complexity_terms = [
            "architecture", "multi-account", "enterprise", "migration", "integration",
            "disaster recovery", "high availability", "scalability", "performance"
        ]

        # Medium complexity indicators
        medium_complexity_terms = [
            "configure", "setup", "deploy", "security group", "vpc", "subnet",
            "load balancer", "auto scaling", "monitoring"
        ]

        if any(term in question_lower for term in high_complexity_terms):
            return "high"
        elif any(term in question_lower for term in medium_complexity_terms):
            return "medium"
        else:
            return "low"

    def _assess_context_quality(self, context: str, question: str) -> Dict[str, Any]:
        """Assess the quality and relevance of retrieved context."""
        aws_services = extract_aws_services(context)

        quality_metrics = {
            "context_length": len(context),
            "has_aws_services": len(aws_services) > 0,
            "aws_services_count": len(aws_services),
            "has_procedural_content": any(term in context.lower() for term in [
                "step", "procedure", "process", "workflow", "guideline"
            ]),
            "relevance_score": self._calculate_relevance_score(context, question),
            "completeness_score": min(len(context) / 5000, 1.0)  # Normalized to 1.0
        }

        return quality_metrics

    def _calculate_relevance_score(self, context: str, question: str) -> float:
        """
        Calculate a simple relevance score between context and question.

        Args:
            context: Retrieved context
            question: User question

        Returns:
            float: Relevance score between 0 and 1
        """
        if not context or not question:
            return 0.0

        # Simple keyword overlap scoring
        question_words = set(question.lower().split())
        context_words = set(context.lower().split())

        # Remove common stop words
        stop_words = {"the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"}
        question_words = question_words - stop_words
        context_words = context_words - stop_words

        if not question_words:
            return 0.0

        # Calculate overlap ratio
        overlap = len(question_words.intersection(context_words))
        return min(overlap / len(question_words), 1.0)