#!/bin/bash
# Script to update .env file for dual AWS account configuration
# Preserves existing Bedrock credentials, adds new S3 credentials

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() { echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%H:%M:%S')] WARNING: $1${NC}"; }
error() { echo -e "${RED}[$(date +'%H:%M:%S')] ERROR: $1${NC}"; exit 1; }
info() { echo -e "${BLUE}[$(date +'%H:%M:%S')] INFO: $1${NC}"; }

# Configuration
APP_DIR="/opt/chainlit_rag"
ENV_FILE="$APP_DIR/.env"
BACKUP_FILE="$APP_DIR/.env.backup.$(date +%Y%m%d_%H%M%S)"

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root (use sudo)"
    fi
}

# Get current environment values
get_current_values() {
    log "Reading current environment configuration..."
    
    if [[ ! -f "$ENV_FILE" ]]; then
        error "Environment file not found: $ENV_FILE"
    fi
    
    # Read current values
    CURRENT_BEDROCK_ACCESS_KEY=$(grep "^AWS_ACCESS_KEY_ID=" "$ENV_FILE" | cut -d'=' -f2 || echo "")
    CURRENT_BEDROCK_SECRET_KEY=$(grep "^AWS_SECRET_ACCESS_KEY=" "$ENV_FILE" | cut -d'=' -f2 || echo "")
    CURRENT_BEDROCK_REGION=$(grep "^AWS_REGION=" "$ENV_FILE" | cut -d'=' -f2 || echo "")
    CURRENT_S3_BUCKET=$(grep "^S3_BUCKET_NAME=" "$ENV_FILE" | cut -d'=' -f2 || echo "")
    CURRENT_EMBEDDING_MODEL=$(grep "^BEDROCK_EMBEDDING_MODEL_ID=" "$ENV_FILE" | cut -d'=' -f2 || echo "")
    CURRENT_LLM_MODEL=$(grep "^BEDROCK_LLM_MODEL_ID=" "$ENV_FILE" | cut -d'=' -f2 || echo "")
    CURRENT_LLM_PROFILE=$(grep "^BEDROCK_LLM_PROFILE_ARN=" "$ENV_FILE" | cut -d'=' -f2 || echo "")
    
    info "Current Bedrock Account: ${CURRENT_BEDROCK_ACCESS_KEY:0:8}..."
    info "Current Bedrock Region: $CURRENT_BEDROCK_REGION"
    info "Current S3 Bucket: $CURRENT_S3_BUCKET"
}

# Prompt for new S3 configuration
get_new_s3_config() {
    log "🔧 New S3 Configuration Setup"
    echo ""
    
    # Get new S3 bucket name
    read -p "Enter the new S3 bucket name: " NEW_S3_BUCKET
    if [[ -z "$NEW_S3_BUCKET" ]]; then
        error "S3 bucket name cannot be empty"
    fi
    
    # Get new AWS credentials for S3
    read -p "Enter AWS Access Key ID for S3 account: " NEW_S3_ACCESS_KEY
    if [[ -z "$NEW_S3_ACCESS_KEY" ]]; then
        error "AWS Access Key ID cannot be empty"
    fi
    
    read -s -p "Enter AWS Secret Access Key for S3 account: " NEW_S3_SECRET_KEY
    echo ""
    if [[ -z "$NEW_S3_SECRET_KEY" ]]; then
        error "AWS Secret Access Key cannot be empty"
    fi
    
    # Get AWS region for S3
    read -p "Enter AWS region for S3 bucket (default: $CURRENT_BEDROCK_REGION): " NEW_S3_REGION
    NEW_S3_REGION=${NEW_S3_REGION:-$CURRENT_BEDROCK_REGION}
    
    echo ""
    log "Configuration Summary:"
    info "OLD S3 Bucket: $CURRENT_S3_BUCKET"
    info "NEW S3 Bucket: $NEW_S3_BUCKET"
    info "NEW S3 Access Key: ${NEW_S3_ACCESS_KEY:0:8}..."
    info "NEW S3 Region: $NEW_S3_REGION"
    info "Bedrock Account (unchanged): ${CURRENT_BEDROCK_ACCESS_KEY:0:8}..."
    echo ""
    
    read -p "Continue with this configuration? (y/N): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        error "Configuration cancelled"
    fi
}

# Create backup
create_backup() {
    log "Creating backup of current .env file..."
    cp "$ENV_FILE" "$BACKUP_FILE"
    log "Backup created: $BACKUP_FILE"
}

# Update environment file
update_env_file() {
    log "Updating .env file with dual AWS account configuration..."
    
    # Create new .env file with dual account configuration
    cat > "$ENV_FILE" << EOF
# AWS Configuration for Bedrock (Your Account)
AWS_ACCESS_KEY_ID=${CURRENT_BEDROCK_ACCESS_KEY}
AWS_SECRET_ACCESS_KEY=${CURRENT_BEDROCK_SECRET_KEY}
AWS_REGION=${CURRENT_BEDROCK_REGION}

# S3 Configuration (Different Account)
S3_BUCKET_NAME=${NEW_S3_BUCKET}
S3_AWS_ACCESS_KEY_ID=${NEW_S3_ACCESS_KEY}
S3_AWS_SECRET_ACCESS_KEY=${NEW_S3_SECRET_KEY}
S3_AWS_REGION=${NEW_S3_REGION}

# Vector Store Configuration
QDRANT_PATH=./vector_store

# Bedrock Configuration (Your Account)
BEDROCK_EMBEDDING_MODEL_ID=${CURRENT_EMBEDDING_MODEL}
BEDROCK_LLM_MODEL_ID=${CURRENT_LLM_MODEL}
BEDROCK_LLM_PROFILE_ARN=${CURRENT_LLM_PROFILE}

# Application Configuration
RAG_API_HOST=0.0.0.0
RAG_API_PORT=8080
API_BASE_URL=http://localhost:8080
CHAINLIT_HOST=0.0.0.0
CHAINLIT_PORT=80
EOF
    
    # Set proper permissions
    chown raguser:raguser "$ENV_FILE"
    chmod 600 "$ENV_FILE"
    
    log "✅ Environment file updated successfully"
}

# Display the new configuration
show_new_config() {
    log "📋 New Environment Configuration:"
    echo ""
    echo -e "${BLUE}=== Bedrock Account (Your Account) ===${NC}"
    echo "AWS_ACCESS_KEY_ID=${CURRENT_BEDROCK_ACCESS_KEY:0:8}..."
    echo "AWS_REGION=$CURRENT_BEDROCK_REGION"
    echo "BEDROCK_EMBEDDING_MODEL_ID=$CURRENT_EMBEDDING_MODEL"
    echo "BEDROCK_LLM_MODEL_ID=$CURRENT_LLM_MODEL"
    echo ""
    echo -e "${BLUE}=== S3 Account (Different Account) ===${NC}"
    echo "S3_BUCKET_NAME=$NEW_S3_BUCKET"
    echo "S3_AWS_ACCESS_KEY_ID=${NEW_S3_ACCESS_KEY:0:8}..."
    echo "S3_AWS_REGION=$NEW_S3_REGION"
    echo ""
}

# Test the new configuration
test_config() {
    log "🧪 Testing new configuration..."
    
    # Test if environment variables are readable
    cd "$APP_DIR"
    
    # Create a simple test script
    cat > "/tmp/test_env.py" << 'EOF'
import os
from dotenv import load_dotenv

load_dotenv()

print("=== Environment Variables Test ===")
print(f"Bedrock Access Key: {os.getenv('AWS_ACCESS_KEY_ID', 'NOT_SET')[:8]}...")
print(f"Bedrock Region: {os.getenv('AWS_REGION', 'NOT_SET')}")
print(f"S3 Bucket: {os.getenv('S3_BUCKET_NAME', 'NOT_SET')}")
print(f"S3 Access Key: {os.getenv('S3_AWS_ACCESS_KEY_ID', 'NOT_SET')[:8]}...")
print(f"S3 Region: {os.getenv('S3_AWS_REGION', 'NOT_SET')}")
print(f"Embedding Model: {os.getenv('BEDROCK_EMBEDDING_MODEL_ID', 'NOT_SET')}")

# Check if all required variables are set
required_vars = ['AWS_ACCESS_KEY_ID', 'AWS_SECRET_ACCESS_KEY', 'AWS_REGION', 
                'S3_BUCKET_NAME', 'S3_AWS_ACCESS_KEY_ID', 'S3_AWS_SECRET_ACCESS_KEY']

missing = [var for var in required_vars if not os.getenv(var)]

if missing:
    print(f"\n❌ Missing variables: {', '.join(missing)}")
    exit(1)
else:
    print("\n✅ All required variables are set")
EOF
    
    # Run the test
    if sudo -u raguser venv/bin/python /tmp/test_env.py; then
        log "✅ Environment configuration test passed"
    else
        error "❌ Environment configuration test failed"
    fi
    
    # Clean up
    rm -f /tmp/test_env.py
}

# Show next steps
show_next_steps() {
    echo ""
    log "🎯 Next Steps:"
    echo ""
    info "1. Restart RAG services:"
    echo "   sudo systemctl restart rag-api.service rag-frontend.service"
    echo ""
    info "2. Test S3 ingestion:"
    echo "   curl -X POST http://localhost:8080/ingest"
    echo ""
    info "3. Test query functionality:"
    echo "   curl -X POST http://localhost:8080/query/advanced \\"
    echo "     -H 'Content-Type: application/json' \\"
    echo "     -d '{\"question\": \"What is AWS?\"}'"
    echo ""
    info "4. If issues occur, restore backup:"
    echo "   sudo cp $BACKUP_FILE $ENV_FILE"
    echo "   sudo systemctl restart rag-api.service rag-frontend.service"
    echo ""
}

# Main execution
main() {
    log "🚀 Starting .env file update for dual AWS account configuration"
    echo ""
    
    check_root
    get_current_values
    get_new_s3_config
    create_backup
    update_env_file
    show_new_config
    test_config
    show_next_steps
    
    echo ""
    log "🎉 .env file update completed successfully!"
    echo ""
    warn "Remember to restart your services to apply the new configuration"
}

# Run main function
main "$@"
